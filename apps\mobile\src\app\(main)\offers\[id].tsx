import { useLocalSearchParams, useRouter } from 'expo-router';
import { KeyboardAvoidingView, Platform } from 'react-native';

import { ChatHeader, OfferInfoHeader, MessageInput } from '@/pages/messages';
import { LoadingScreen, ErrorScreen, MessagesList } from '@/pages/offers';

import {
  useOfferDetail,
  useOfferMessages,
  useMeetingProposals,
} from '@/shared/model';
import { VStack } from '@/shared/ui';

export default function OfferDetail() {
  const router = useRouter();
  const { id: offerId } = useLocalSearchParams();

  // Custom hooks for managing state and logic
  const {
    offer,
    isLoading,
    error,
    currentUserId,
    isOfferCreator,
    isNeedCreator,
    formatDate,
  } = useOfferDetail({
    offerId: offerId as string,
  });

  const {
    messages,
    isLoadingMessages,
    inputMessage,
    setInputMessage,
    handleSendMessage,
    setMessages,
  } = useOfferMessages({
    offerId: offerId as string,
  });

  const { handleMakeOffer, handleAcceptMeeting, handleDeclineMeeting } =
    useMeetingProposals({
      offerId: offerId as string,
      messages,
      setMessages,
    });

  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error || !offer) {
    return <ErrorScreen message={error || 'Offer not found'} />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      className="bg-gray-50"
    >
      <ChatHeader
        firstName={
          isNeedCreator ? offer.user.firstName : offer.need.user.firstName
        }
        lastName={
          isNeedCreator ? offer.user.lastName : offer.need.user.lastName
        }
        imageUrl="https://randomuser.me/api/portraits/men/32.jpg" // Placeholder
        isOnline={true} // Placeholder
        onBack={handleBack}
      />

      <OfferInfoHeader
        title={offer.need.title}
        description={offer.need.description}
        price={offer.price.toString()}
        status={offer.status}
        location="Unknown" // This information is not available in the API response
      />

      <VStack className="flex-1" space="md">
        <MessagesList
          messages={messages}
          isLoadingMessages={isLoadingMessages}
          currentUserId={currentUserId}
          formatDate={formatDate}
          onAcceptMeeting={handleAcceptMeeting}
          onDeclineMeeting={handleDeclineMeeting}
        />

        <MessageInput
          message={inputMessage}
          setMessage={setInputMessage}
          onSend={handleSendMessage}
          onMakeOffer={handleMakeOffer}
          showMakeOfferButton={!isNeedCreator} // Only show for offer creators, not need creators
        />
      </VStack>
    </KeyboardAvoidingView>
  );
}
