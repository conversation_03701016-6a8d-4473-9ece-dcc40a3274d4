import React from 'react';
import { 
  MessageBubbleProps, 
  RegularMessageProps, 
  MeetingProposalProps, 
  AcceptedMeetingProps, 
  DeclinedMeetingProps 
} from './types/message-types';
import RegularMessage from './bubble-types/RegularMessage';
import MeetingProposal from './bubble-types/MeetingProposal';
import AcceptedMeeting from './bubble-types/AcceptedMeeting';
import DeclinedMeeting from './bubble-types/DeclinedMeeting';

export function MessageBubble(props: MessageBubbleProps) {
  // Le composant parent agit comme un "switch" qui délègue l'affichage
  // au composant enfant approprié selon le type de message
  switch (props.type) {
    case 'regular':
      return <RegularMessage {...props as RegularMessageProps} />;
    case 'proposal':
      return <MeetingProposal {...props as MeetingProposalProps} />;
    case 'accepted':
      return <AcceptedMeeting {...props as AcceptedMeetingProps} />;
    case 'declined':
      return <DeclinedMeeting {...props as DeclinedMeetingProps} />;
    default:
      // TypeScript devrait empêcher d'arriver ici, mais au cas où
      console.error('Unknown message type:', props.type);
      return null;
  }
}
