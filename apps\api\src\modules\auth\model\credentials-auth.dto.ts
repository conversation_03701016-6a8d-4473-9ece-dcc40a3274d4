import { z } from 'zod';

import { userCreateDto } from '@/modules/users/user.dto';

export const signUpDto = userCreateDto
  .pick({
    email: true,
    firstName: true,
    lastName: true,
    imageRef: true,
  })
  .extend({
    password: z.string().min(8).max(20),
  });
export type SignUpDto = z.infer<typeof signUpDto>;

export const signInDto = userCreateDto
  .pick({
    email: true,
  })
  .extend({
    password: z.string(),
  });
export type SignInDto = z.infer<typeof signInDto>;
