# API Helpers et React Query Integration

## Nouvelles fonctions helper

### `fetchJson<T>(url, options?)`
Fonction helper pour les requêtes JSON qui retourne un `CatchErrorResult<T, HttpError | SyntaxError | Error>`.

```typescript
const [error, data] = await fetchJson<{ user: User }>('/api/users/123');

if (error) {
  // Gérer l'erreur
  return;
}

// Utiliser data.user
```

### `fetchExists(url)`
Fonction helper pour les requêtes HEAD/GET simples qui retourne un `CatchErrorResult<boolean, HttpError | Error>`.

```typescript
const [error, exists] = await fetchExists('/api/users/123');

if (error) {
  // Gérer l'erreur
  return;
}

// exists est un boolean
```

## Hooks React Query

### Pattern recommandé avec gestion d'erreurs

```typescript
// Dans le composant
const signUpMutation = useSignUp();

const handleSubmit = async (data) => {
  try {
    const result = await signUpMutation.mutateAsync(data);
    // Succès - result contient directement les données
    router.push('/success');
  } catch (error) {
    // Gestion d'erreurs avec switch
    switch (error.constructor) {
      case HttpError:
        console.error('HTTP Error:', error.message);
        // Gérer erreur HTTP (400, 500, etc.)
        break;
      case SyntaxError:
        console.error('JSON parsing error:', error.message);
        // Gérer erreur de parsing JSON
        break;
      default:
        console.error('Unknown error:', error);
        // Gérer autres erreurs
    }
  }
};
```

## Migration des fonctions API

### Avant (avec nativeFetch direct)
```typescript
export async function signUp(data: SignUpData) {
  const [error, response] = await nativeFetch('/api/auth/sign-up', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (error) {
    return [error, undefined];
  }

  const [jsonErr, jsonData] = await response.json();
  if (jsonErr) return [jsonErr, undefined];

  return [undefined, jsonData];
}
```

### Après (avec fetchJson)
```typescript
export async function signUp(data: SignUpData): Promise<CatchErrorResult<SignUpResponse, HttpError | SyntaxError | Error>> {
  return fetchJson<SignUpResponse>('/api/auth/sign-up', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
}
```

## Hooks React Query correspondants

```typescript
export function useSignUp() {
  return useMutation({
    mutationFn: (data: SignUpData) => handleCatchErrorResult(signUp(data)),
  });
}
```

La fonction `handleCatchErrorResult` convertit le `CatchErrorResult` en exception pour React Query.
