# API Helpers and React Query Integration

## New Helper Functions

### `fetchJson<T>(url, options?)`

Helper function for JSON requests that returns a `CatchErrorResult<T, HttpError | SyntaxError | Error>`.

```typescript
const [error, data] = await fetchJson<{ user: User }>('/api/users/123');

if (error) {
  // Handle error
  return;
}

// Use data.user
```

### `fetchExists(url)`

Helper function for simple HEAD/GET requests that returns a `CatchErrorResult<boolean, HttpError | Error>`.

```typescript
const [error, exists] = await fetchExists('/api/users/123');

if (error) {
  // Handle error
  return;
}

// exists is a boolean
```

## React Query Hooks

### Recommended Pattern with Error Handling

```typescript
// In the component
const signUpMutation = useSignUp();

const handleSubmit = async (data) => {
  try {
    const result = await signUpMutation.mutateAsync(data);
    // Success - result contains the data directly
    router.push('/success');
  } catch (error) {
    // Error handling with switch
    switch (error.constructor) {
      case HttpError:
        console.error('HTTP Error:', error.message);
        // Handle HTTP error (400, 500, etc.)
        break;
      case SyntaxError:
        console.error('JSON parsing error:', error.message);
        // Handle JSON parsing error
        break;
      default:
        console.error('Unknown error:', error);
      // Handle other errors
    }
  }
};
```

## API Functions Migration

### Before (with direct nativeFetch)

```typescript
export async function signUp(data: SignUpData) {
  const [error, response] = await nativeFetch('/api/auth/sign-up', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (error) {
    return [error, undefined];
  }

  const [jsonErr, jsonData] = await response.json();
  if (jsonErr) return [jsonErr, undefined];

  return [undefined, jsonData];
}
```

### After (with fetchJson)

```typescript
export async function signUp(data: SignUpData): Promise<CatchErrorResult<SignUpResponse, HttpError | SyntaxError | Error>> {
  return fetchJson<SignUpResponse>('/api/auth/sign-up', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
}
```

## Corresponding React Query Hooks

```typescript
export function useSignUp() {
  return useMutation({
    mutationFn: (data: SignUpData) => handleCatchErrorResult(signUp(data)),
  });
}
```

The `handleCatchErrorResult` function converts the `CatchErrorResult` to an exception for React Query.
