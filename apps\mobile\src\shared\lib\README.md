# API Helpers and React Query Integration

## New Helper Functions

### `fetchJson<T>(url, options?)`

Helper function for JSON requests that returns a `CatchErrorResult<T, HttpError | SyntaxError | Error>`.

```typescript
const [error, data] = await fetchJson<{ user: User }>('/api/users/123');

if (error) {
  // Handle error
  return;
}

// Use data.user
```

### `fetchExists(url)`

Helper function for simple HEAD/GET requests that returns a `CatchErrorResult<boolean, HttpError | Error>`.

```typescript
const [error, exists] = await fetchExists('/api/users/123');

if (error) {
  // Handle error
  return;
}

// exists is a boolean
```

## React Query Integration

### API Functions

API functions continue to return `CatchErrorResult` for consistency:

```typescript
export async function signUp(data: SignUpData): Promise<CatchErrorResult<SignUpResponse, HttpError | SyntaxError | Error>> {
  return fetchJson<SignUpResponse>('/api/auth/sign-up', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
}
```

### Using useMutation in Components

```typescript
// In the component
const signUpMutation = useMutation({
  mutationFn: async (data: SignUpData) => {
    const [error, result] = await signUp(data);
    if (error) {
      throw error; // React Query will catch this
    }
    return result;
  },
});

const handleSubmit = (data: SignUpData) => {
  signUpMutation.mutate(data, {
    onSuccess: (result) => {
      // Handle success
      router.push('/success');
    },
    onError: (error) => {
      // Error handling with switch
      switch (error.constructor) {
        case HttpError:
          console.error('HTTP Error:', error.message);
          break;
        case SyntaxError:
          console.error('JSON parsing error:', error.message);
          break;
        default:
          console.error('Unknown error:', error);
      }
    },
  });
};
```

### Using useQuery in Components

```typescript
// For data fetching
const { data, error, isLoading } = useQuery({
  queryKey: ['categories'],
  queryFn: async () => {
    const [error, result] = await getCategories();
    if (error) {
      throw error; // React Query will catch this
    }
    return result.categories;
  },
});
```

## Benefits of This Approach

1. **Consistent API**: All API functions return `CatchErrorResult`
2. **React Query Integration**: Errors are properly thrown for React Query to handle
3. **Centralized Error Handling**: Use `onError` callbacks for consistent error handling
4. **Type Safety**: Full TypeScript support with proper error types
5. **Separation of Concerns**: API logic separate from UI logic
