import { zodResolver } from '@hookform/resolvers/zod';
import { createOfferSchema } from '@/pages/needs';
import { useOfferStore } from '@/shared/model';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogBody,
  Button,
  ButtonText,
  Text,
  AlertDialogBackdrop,
  AlertDialogHeader,
  Heading,
  VStack,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  AlertCircleIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
} from '@/shared/ui';

import React from 'react';
import { Controller, useForm } from 'react-hook-form';

interface Props {
  needId: string;
}

export function CreateOfferDialog({ needId }: Props) {
  const { addOffer, isLoading } = useOfferStore();
  const [showAlertDialog, setShowAlertDialog] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleClose = () => {
    setShowAlertDialog(false);
    setError(null);
  };

  const form = useForm({
    resolver: zodResolver(createOfferSchema),
    defaultValues: {
      price: 0,
      message: '',
    },
  });

  const handleCreation = async () => {
    setError(null);
    const { price, message } = form.getValues();

    try {
      await addOffer({ price, message, needId });
      handleClose();
      // Reset form after successful submission
      form.reset();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create offer');
      console.error('Error creating offer:', err);
    }
  };

  return (
    <>
      <Button onPress={() => setShowAlertDialog(true)} action="positive">
        <ButtonText>Make an offer</ButtonText>
      </Button>
      <AlertDialog isOpen={showAlertDialog} onClose={handleClose} size="md">
        <AlertDialogBackdrop />
        <AlertDialogContent>
          <AlertDialogHeader>
            <Heading className="text-typography-950 font-semibold" size="md">
              How much do you want for fulfilling this need?
            </Heading>
          </AlertDialogHeader>
          <AlertDialogBody className="mt-3 mb-4">
            <VStack>
              <Controller
                control={form.control}
                name="price"
                render={({ field, fieldState }) => (
                  <FormControl
                    isInvalid={fieldState.error !== undefined}
                    size="md"
                  >
                    <FormControlLabel>
                      <FormControlLabelText className="text-typography-500">
                        Price
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input>
                      <InputField
                        type="text"
                        autoCapitalize="none"
                        {...field}
                        value={field.value.toString()}
                        onChangeText={field.onChange}
                      />
                    </Input>
                    <FormControlError>
                      <FormControlErrorIcon as={AlertCircleIcon} />
                      <FormControlErrorText>
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                )}
              />
              <Controller
                control={form.control}
                name="message"
                render={({ field, fieldState }) => (
                  <FormControl
                    isInvalid={fieldState.error !== undefined}
                    size="md"
                  >
                    <FormControlLabel>
                      <FormControlLabelText className="text-typography-500">
                        Message
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input>
                      <InputField
                        type="text"
                        autoCapitalize="none"
                        {...field}
                        onChangeText={field.onChange}
                      />
                    </Input>
                    <FormControlError>
                      <FormControlErrorIcon as={AlertCircleIcon} />
                      <FormControlErrorText>
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                )}
              />
            </VStack>
          </AlertDialogBody>
          <AlertDialogFooter className="">
            {error && <Text className="text-red-500 mb-2">{error}</Text>}
            <Button
              variant="outline"
              action="secondary"
              onPress={handleClose}
              size="sm"
              isDisabled={isLoading}
            >
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button
              size="sm"
              className="bg-blue-600"
              onPress={form.handleSubmit(handleCreation)}
              isDisabled={isLoading}
            >
              <ButtonText>Create</ButtonText>
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
