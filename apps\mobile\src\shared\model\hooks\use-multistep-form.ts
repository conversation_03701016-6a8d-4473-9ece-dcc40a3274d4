import { ReactElement, useCallback, useMemo, useState } from 'react';

import { UseFormReturn } from 'react-hook-form';

export function useMultistepForm(
  steps: ReactElement[],
  forms: UseFormReturn<any>[]
) {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [stepStack, setStepStack] = useState<number[]>([0]);

  const isFirstStep = currentStepIndex <= 0;
  const isLastStep = currentStepIndex >= steps.length - 1;

  const currentStep = steps[currentStepIndex];
  const currentForm = forms[currentStepIndex];

  const nextStep = useCallback(
    () =>
      !isLastStep &&
      setCurrentStepIndex((i) => {
        setStepStack((prev) => [...prev, i + 1]);
        return i + 1;
      }),
    [isLastStep, stepStack]
  );

  const previeousStep = useCallback(
    () =>
      !isFirstStep &&
      setCurrentStepIndex((i) => {
        setStepStack((prev) => prev.slice(0, prev.length - 1));
        return stepStack[stepStack.length - 2];
      }),
    [isFirstStep, stepStack]
  );

  const goToStep = useCallback(
    (stepIndex: number) => {
      setCurrentStepIndex(() => {
        if (stepIndex >= steps.length || stepIndex < 0) {
          console.warn(`Step index must be between 0 and ${steps.length - 1}.`);
          return currentStepIndex;
        }

        setStepStack((prev) => [...prev, stepIndex]);
        return stepIndex;
      });
    },
    [currentStepIndex, stepStack]
  );

  return useMemo(
    () => ({
      currentStepIndex,
      currentStep,
      currentForm,
      isFirstStep,
      isLastStep,
      nextStep,
      previeousStep,
      goToStep,
    }),
    [
      currentStepIndex,
      currentStep,
      currentForm,
      isFirstStep,
      isLastStep,
      nextStep,
      previeousStep,
      goToStep,
    ]
  );
}
