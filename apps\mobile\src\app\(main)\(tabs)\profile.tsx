import { Redirect, useRouter } from 'expo-router';
import { Text, View } from 'react-native';

import { Clock8, <PERSON>Helping, MessageCircle, Star } from 'lucide-react-native';

import { signOut } from '@/shared/api';
import { useAuth } from '@/shared/model';
import {
  Avatar,
  AvatarBadge,
  AvatarFallbackText,
  AvatarImage,
  Button,
  ButtonText,
  Divider,
  Heading,
  HStack,
  VStack,
} from '@/shared/ui';
import { useState } from 'react';

export default function Index() {
  // Get user session and loading state
  const { session, isLoading: isLoadingSession } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('review');
  /// If the user is not authenticated and the session check is done
  // Redirect them to the Sign-In page
  if (!session && !isLoadingSession) {
    return <Redirect href="/(auth)/sign-in" />;
  }
  // Function that logs out the user and redirects them to Sign-In page
  const handleSignOut = async () => {
    await signOut();
    router.push('/sign-in');
  };

  return (
    <View
    // style={{
    //   flex: 1,
    //   justifyContent: 'center',
    //   alignItems: 'center',
    // }}
    >
      {/* HEADER — User Info Section */}
      <HStack
        className="items-center w-full px-4 py-2 bg-white shadow-sm"
        space="md"
      >
        {/* User Avatar with fallback text if no image */}
        <Avatar size="xl">
          <AvatarFallbackText>{`${session?.user.firstName} ${session?.user.lastName}`}</AvatarFallbackText>
          <AvatarImage
            source={{
              uri: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
            }}
          />
        </Avatar>
        {/* User Information displayed next to Avatar */}
        <VStack>
          {/* Full name */}
          <Heading size="xl">{`${session?.user.firstName} ${session?.user.lastName}`}</Heading>
          {/* User Location (Static Text for now) */}
          <Text className="text-m color-gray-500">Paris, France</Text>
          {/* User Rating */}
          <HStack space="xs" className="items-center">
            <Star size={20} fill="hsl(220, 100%, 50%)" />
            <Text>
              <Text className="font-bold">4.8</Text> <Text>(24 reviews)</Text>
            </Text>
          </HStack>
        </VStack>
      </HStack>

      <Divider />
      {/* USER STATS SECTION */}
      <HStack
        className="items-center justify-between w-full px-4 py-2 bg-white shadow-sm"
        space="md"
      >
        <VStack className="items-center" space="xs">
          {/* Number of fulfilled requests */}
          <Text className="font-bold">80</Text>
          <Heading size="sm" className="font-normal italic">
            Fulfilled needs
          </Heading>
        </VStack>
        {/* Average Response Time */}
        <VStack className="items-center" space="xs">
          <Text className="font-bold">3 min</Text>
          <Heading size="sm" className="font-normal italic">
            Responses
          </Heading>
        </VStack>
        {/* Registration Year */}
        <VStack className="items-center" space="xs">
          <Text className="font-bold">2024</Text>
          <Heading size="sm" className="font-normal italic">
            Member since
          </Heading>
        </VStack>
      </HStack>

      {/* BADGES SECTION — User Qualities */}
      <HStack
        className="items-center justify-between w-full px-4 py-2 bg-white shadow-sm"
        space="md"
      >
        {/* Each VStack represents a Badge */}

        <VStack className="items-center" space="xs">
          <View className="bg-blue-300 rounded-full stroke-blue-600 p-3">
            <HandHelping
              strokeWidth={1}
              size={48}
              color={'hsl(220, 100%, 50%)'}
            />
          </View>
          <Heading size="sm" className="font-normal italic">
            Super Helper
          </Heading>
        </VStack>

        <VStack className="items-center" space="xs">
          <View className="bg-green-300 rounded-full stroke-green-600 p-3">
            <Clock8 strokeWidth={1} size={48} color={'hsl(120, 100%, 30%)'} />
          </View>
          <Heading size="sm" className="font-normal italic">
            Ponctual
          </Heading>
        </VStack>

        <VStack className="items-center" space="xs">
          <View className="bg-purple-300 rounded-full stroke-purple-600 p-3">
            <MessageCircle
              strokeWidth={1}
              size={48}
              color={'hsl(280, 100%, 50%)'}
            />
          </View>
          <Heading size="sm" className="font-normal italic">
            Communicative
          </Heading>
        </VStack>

        <VStack className="items-center" space="xs">
          <View className="bg-yellow-300 rounded-full stroke-yellow-600 p-3">
            <Star strokeWidth={1} size={48} color={'hsl(30, 100%, 50%)'} />
          </View>
          <Heading size="sm" className="font-normal italic">
            5 stars
          </Heading>
        </VStack>
      </HStack>

      <Divider />
      {/* TABS SECTION — For Switching between Review and History */}
      <HStack
        className="items-center justify-center w-full px-4 py-2 bg-white shadow-sm "
        space="md"
      >
        {/* Reviews Tab */}
        <Button
          className="w-1/2"
          variant="tab"
          isPressed={activeTab === 'review'}
          onPress={() => setActiveTab('review')}
        >
          <ButtonText>Reviews</ButtonText>
        </Button>
        {/* History Tab */}
        <Button
          className="w-1/2"
          variant="tab"
          isPressed={activeTab === 'history'}
          onPress={() => setActiveTab('history')}
        >
          <ButtonText>History</ButtonText>
        </Button>

        <Button
          variant="solid"
          className="bg-primary-500 mt-4 mx-4"
          onPress={() => router.push('/(main)/subscribe')}
        >
          <ButtonText className="text-white">S'abonner à Premium</ButtonText>
        </Button>
      </HStack>

      {/* LOGOUT BUTTON */}
      <Button onPress={handleSignOut} className="text-4xl">
        <ButtonText>Sign Out</ButtonText>
      </Button>
    </View>
  );
}
