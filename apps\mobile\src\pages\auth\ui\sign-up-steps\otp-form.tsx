import { Controller, UseFormReturn } from 'react-hook-form';

import { noOfDigits, OtpSchema } from '@/pages/auth';

import {
  AlertCircleIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  PinInput,
  PinInputField,
  Text,
  VStack,
} from '@/shared/ui';

interface Props {
  form: UseFormReturn<OtpSchema>;
}

export function OTPForm({ form }: Props) {
  return (
    <VStack space="md">
      <Text className="text-center mb-2">
        Please enter the verification code sent to your email
      </Text>

      <Controller
        control={form.control}
        name="code"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText>Verification Code</FormControlLabelText>
            </FormControlLabel>
            <PinInput
              noOfFields={noOfDigits}
              {...field}
              onChange={field.onChange}
            >
              {Array.from({ length: noOfDigits }).map((_, index) => (
                <PinInputField key={index} index={index} />
              ))}
            </PinInput>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />
    </VStack>
  );
}
