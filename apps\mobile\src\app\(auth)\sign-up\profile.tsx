import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';

import { useState } from 'react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  profileSchema,
  ProfileSchema,
  ProfileSignUpForm,
  signUp,
  uploadMedia,
  useSignUpStore,
} from '@/pages/auth';

import {
  Button,
  ButtonText,
  Center,
  Heading,
  HStack,
  VStack,
} from '@/shared/ui';

export default function SignUpProfilePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { credentials, profile, setProfile } = useSignUpStore();

  const form = useForm<ProfileSchema>({
    resolver: zodResolver(profileSchema),
    defaultValues: profile,
  });

  // Handle image selection
  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      const imageUri = result.assets[0].uri;
      form.setValue('imageUri', imageUri);
      setProfile({ imageUri });
    }
  };

  // Handle image capture
  const takePhoto = async () => {
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

    if (cameraPermission.status !== 'granted') {
      alert('We need camera permission to take a picture');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      const imageUri = result.assets[0].uri;
      form.setValue('imageUri', imageUri);
      setProfile({ imageUri });
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleSubmit = async (data: ProfileSchema) => {
    try {
      setIsLoading(true);
      setProfile(data);

      let imageRef;

      // Upload image if provided
      if (data.imageUri) {
        // Upload the image directly using the URI
        const result = await uploadMedia(data.imageUri);
        imageRef = result.imageRef;
      }

      // Submit sign-up data
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      const signUpData = {
        email: credentials.email,
        password: credentials.password,
        firstName: data.firstName,
        lastName: data.lastName,
        imageRef,
      };

      const [error, response] = await signUp(signUpData);

      if (error) {
        console.error(error);
        return;
      }

      // Extract userId from response
      const userId = await response.json().then((data) => data.user.id);

      // Navigate to OTP verification with userId as parameter
      router.push({
        pathname: '/sign-up/otp',
        params: { userId },
      });
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center className="h-full p-6 -mt-6 bg-background-0">
      <VStack space="lg" className="mx-6 w-full">
        <VStack space="md" className="min-w-full">
          <Heading size="3xl" className="text-typography-900">
            Create your profile
          </Heading>

          <ProfileSignUpForm
            form={form}
            onPickImage={pickImage}
            onTakePhoto={takePhoto}
          />

          <HStack space="md" className="mt-4">
            <Button
              onPress={handleBack}
              size="xl"
              variant="outline"
              className="flex-1"
            >
              <ButtonText>Back</ButtonText>
            </Button>

            <Button
              className="bg-blue-600 flex-1"
              onPress={form.handleSubmit(handleSubmit)}
              size="xl"
              isDisabled={isLoading}
            >
              <ButtonText className="text-typography-0">
                {isLoading ? 'Loading...' : 'Continue'}
              </ButtonText>
            </Button>
          </HStack>
        </VStack>
      </VStack>
    </Center>
  );
}
