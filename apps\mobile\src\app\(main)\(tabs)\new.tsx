import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';

import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  AlertCircleIcon,
  Button,
  ButtonText,
  Center,
  ChevronDownIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  Input,
  InputField,
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectTrigger,
  VStack,
  Spinner,
  Text,
} from '@/shared/ui';
import { createNeedSchema, getCategories } from '@/pages/needs';
import { useNeedStore } from '@/shared/model';
import { useQuery } from '@tanstack/react-query';

export default function NeedForm() {
  const router = useRouter();
  const { addNeed } = useNeedStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cachedLocation, setCachedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Pre-fetch location when component mounts
  useEffect(() => {
    const getLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          try {
            // Try to get location with high accuracy first
            console.log('Getting location with high accuracy...');
            const location = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.High,
            });

            console.log('Got high accuracy location:', location.coords);
            setCachedLocation({
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            });
          } catch (highAccuracyError) {
            console.log('High accuracy location failed:', highAccuracyError);

            try {
              // If high accuracy fails, try with lower accuracy
              console.log('Getting location with balanced accuracy...');
              const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
              });

              console.log('Got balanced accuracy location:', location.coords);
              setCachedLocation({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              });
            } catch (lowAccuracyError) {
              console.log('All location attempts failed, using default');
              setCachedLocation({
                latitude: 49.03985836191832,
                longitude: 2.0781613024585637,
              });
            }
          }
        } else {
          console.log('Location permission denied, using default');
          setCachedLocation({
            latitude: 49.03985836191832,
            longitude: 2.0781613024585637,
          });
        }
      } catch (err) {
        console.log('Error pre-fetching location:', err);
        setCachedLocation({
          latitude: 49.03985836191832,
          longitude: 2.0781613024585637,
        });
      }
    };

    getLocation();
  }, []);

  const { data } = useQuery({
    queryKey: ['categories'],
    queryFn: () => getCategories(),
  });

  const form = useForm<z.infer<typeof createNeedSchema>>({
    resolver: zodResolver(createNeedSchema),
  });

  const handleCreation = async () => {
    setIsSubmitting(true);
    setError(null);
    const { title, description, category: categoryId } = form.getValues();

    try {
      // Use cached location if available to avoid delay
      let locationCoords;

      if (cachedLocation) {
        locationCoords = cachedLocation;
        console.log('Using cached location:', locationCoords);
      } else {
        // Try to get a fresh location with better handling for real devices
        try {
          console.log('Getting fresh location with high accuracy...');
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
          });

          console.log('Got fresh high accuracy location:', location.coords);
          locationCoords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
        } catch (highAccuracyError) {
          console.log(
            'Fresh high accuracy location failed:',
            highAccuracyError
          );

          try {
            // If high accuracy fails, try with lower accuracy
            console.log('Getting fresh location with balanced accuracy...');
            const location = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.Balanced,
            });

            console.log(
              'Got fresh balanced accuracy location:',
              location.coords
            );
            locationCoords = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            };
          } catch (lowAccuracyError) {
            console.log('All fresh location attempts failed, using default');
            locationCoords = {
              latitude: 49.03985836191832,
              longitude: 2.0781613024585637,
            };
          }
        }
      }

      // Use the need store to add the need
      await addNeed({
        title,
        description,
        categoryId,
        location: {
          x: locationCoords.latitude,
          y: locationCoords.longitude,
        },
      });

      // Navigate back to the map screen
      router.push('/');
    } catch (error) {
      console.error('Error creating need:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to create need'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <VStack space="sm" className="p-6">
        <Controller
          control={form.control}
          name="category"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined} size="md">
              <Select {...field} onValueChange={field.onChange}>
                <SelectTrigger variant="underlined" size="md">
                  <SelectInput placeholder="Select category" />
                  <SelectIcon className="mr-3" as={ChevronDownIcon} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {data?.map((category) => (
                      <SelectItem
                        key={category.id}
                        label={category.name}
                        value={category.id}
                      />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
              <FormControlError>
                <FormControlErrorIcon as={AlertCircleIcon} />
                <FormControlErrorText>
                  {fieldState.error?.message}
                </FormControlErrorText>
              </FormControlError>
            </FormControl>
          )}
        />

        <Controller
          control={form.control}
          name="title"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined} size="md">
              <Input size="xl" variant="ghost">
                <InputField
                  placeholder="Title"
                  {...field}
                  onChangeText={field.onChange}
                  className="font-black text-4xl placeholder:text-typography-900/20"
                />
              </Input>
              <FormControlError>
                <FormControlErrorIcon as={AlertCircleIcon} />
                <FormControlErrorText>
                  {fieldState.error?.message}
                </FormControlErrorText>
              </FormControlError>
            </FormControl>
          )}
        />

        <Controller
          control={form.control}
          name="description"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined} size="md">
              <Input variant="ghost" size="xl">
                <InputField
                  placeholder="Description"
                  {...field}
                  onChangeText={field.onChange}
                  multiline
                  className="placeholder:text-typography-900/20"
                />
              </Input>
              <FormControlError>
                <FormControlErrorIcon as={AlertCircleIcon} />
                <FormControlErrorText>
                  {fieldState.error?.message}
                </FormControlErrorText>
              </FormControlError>
            </FormControl>
          )}
        />
      </VStack>
      {error && (
        <Center className="mt-4">
          <Text className="text-red-500">{error}</Text>
        </Center>
      )}
      <Center className="absolute bottom-0 w-full mb-4">
        <Button
          size="xl"
          variant="solid"
          action="primary"
          className="bg-blue-600"
          onPress={form.handleSubmit(handleCreation)}
          isDisabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Spinner size="small" color="white" className="mr-2" />
              <ButtonText>Creating...</ButtonText>
            </>
          ) : (
            <ButtonText>Done</ButtonText>
          )}
        </Button>
      </Center>
    </>
  );
}
