import { nativeFetch } from '@/shared/lib';

type SubscribeDto = {
  priceId: string;
};

type SubscribeResponse = {
  url: string;
};

export async function subscribe(dto: SubscribeDto): Promise<SubscribeResponse> {
  const response = await nativeFetch('/api/payments/subscribe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(dto),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to subscribe');
  }

  const data: SubscribeResponse = await response.json();

  return data;
}
