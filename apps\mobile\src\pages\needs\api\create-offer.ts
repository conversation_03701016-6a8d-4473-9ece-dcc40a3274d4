import { nativeFetch } from '@/shared/lib';

type Offer = {
  id: string;
  price: number;
  userId: string;
  needId: string;
  status: string;
  createdAt: Date;
  updatedAt?: Date;
};

type Message = {
  id: string;
  offerId: string;
  senderId: string;
  content: string;
  status: string;
  createdAt: Date;
  updatedAt?: Date;
};

type CreateOfferDto = {
  needId: string;
  price: number;
  message: string;
};

type NewOffer = Offer & {
  message: Message;
};

export async function createOffer(offer: CreateOfferDto) {
  const response = await nativeFetch('/api/offers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...offer,
      price: offer.price.toString(),
    }),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not create offer');
  }

  const data = await response.json();
  return data.offer as NewOffer;
}
