const preset = require('jest-expo/jest-preset');

module.exports = {
  ...preset,
  setupFilesAfterEnv: ['@testing-library/jest-native/extend-expect'],
  transformIgnorePatterns: [
    'node_modules/(?!(' +
      [
        '@react-native',
        'react-native',
        'expo',
        'expo-router',
        'react-native-maps',
        '@gluestack-ui',
        '@expo',
        '@unimodules',
        '@react-navigation',
        'react-native-svg',
        'expo-modules-core',
        '@tanstack',
        '@react-native-aria',
        'react-native-css-interop',
        '@legendapp/motion',
        'nativewind',
      ].join('|') +
      ')/)',
  ],
  moduleNameMapper: {
    '^react$': '<rootDir>/node_modules/react',
    '^react-dom$': '<rootDir>/node_modules/react-dom',
    '^react/jsx-runtime$': '<rootDir>/node_modules/react/jsx-runtime',
    '^react-native$': '<rootDir>/node_modules/react-native',
    '^react-server-dom-webpack$': '<rootDir>/node_modules/react-server-dom-webpack',
    '\\.svg$': '<rootDir>/__mocks__/svgMock.js',
  },
  
};
