import { createContext, useContext, useEffect, useState } from 'react';

import { io, Socket } from 'socket.io-client';

import type { ClientToServerEvents, ServerToClientEvents } from '@needit/socket';

import { backendBaseUrl } from '@/shared/config';
import { useToast, Toast, ToastTitle, ToastDescription } from '@/shared/ui';

type SocketContextType = {
  socket: Socket | null;
  isConnected: boolean;
};

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

export const useSocket = () => useContext(SocketContext);

interface SocketProviderProps {
  children: React.ReactNode;
}

export default function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket<ServerToClientEvents, ClientToServerEvents> | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const toast = useToast();

  useEffect(() => {
    // Initialisation de la connexion socket
    const socketInstance: Socket<ServerToClientEvents, ClientToServerEvents> = io(backendBaseUrl);

    // Gestion des événements de connexion
    socketInstance.on('connect', () => {
      console.log('Socket.io connected');
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('Socket.io disconnected');
      setIsConnected(false);
    });

    // Gestion de l'événement de nouvelle offre
    socketInstance.on(
      'newOffer',
      (data: {
        offerId: string;
        needId: string;
        needTitle: string;
        userId: string;
      }) => {
        console.log('New offer received:', data);

        // Affichage d'un toast pour notifier l'utilisateur
        toast.show({
          placement: 'top',
          duration: 5000,
          render: ({ id }) => {
            const uniqueToastId = 'toast-' + id;
            return (
              <Toast nativeID={uniqueToastId} action="success" variant="solid">
                <ToastTitle>Nouvelle offre reçue!</ToastTitle>
                <ToastDescription>
                  Vous avez reçu une nouvelle offre sur votre demande{' '}
                  {data.needTitle}
                </ToastDescription>
              </Toast>
            );
          },
        });
      }
    );

    setSocket(socketInstance);

    // Nettoyage à la déconnexion
    return () => {
      socketInstance.disconnect();
    };
  }, [toast]);

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
}
