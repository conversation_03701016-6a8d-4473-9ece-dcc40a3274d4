// Import React hooks and shared utilities
import { useState, useEffect, useCallback } from 'react';
import { useOfferStore } from '@/shared/model'; // Store managing offers
import { getSession } from '@/shared/api/auth'; // API to get the current user session

// Interface for hook's input: expects an offer ID
interface UseOfferDetailProps {
  offerId: string;
}

// Interface describing what the hook will return
interface UseOfferDetailReturn {
  offer: any; // TODO: Replace 'any' with a specific type based on your offer model
  isLoading: boolean;
  error: string | null;
  currentUserId: string | null;
  isOfferCreator: boolean;
  isNeedCreator: boolean;
  formatDate: (dateString: string) => string;
}

// Declaration of the custom hook
export const useOfferDetail = ({ offerId }: UseOfferDetailProps): UseOfferDetailReturn => {
  // Get offer data and loading status from the offer store
  const { fetchOfferDetail, selectedOffer, isLoading } = useOfferStore();
  
  // State to store the current user's ID
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  
  // State to store any error messages
  const [error, setError] = useState<string | null>(null);

  // Effect: Fetch the offer details when offerId changes
  useEffect(() => {
    if (offerId) {
      fetchOfferDetail(offerId)
        .catch(err => {
          console.error('Failed to fetch offer details:', err);
          setError('Failed to load offer details');
        });
    }
  }, [offerId, fetchOfferDetail]); // Dependencies: offerId and fetchOfferDetail

  // Effect: Fetch the current user session when the component mounts
  useEffect(() => {
    const fetchSession = async () => {
      try {
        const session = await getSession(); // Get the current session (user info)
        if (session) {
          setCurrentUserId(session.user.id); // Save the user's ID
        }
      } catch (error) {
        console.error('Failed to fetch session:', error);
        setError('Failed to load user session');
      }
    };
    
    fetchSession();
  }, []); // Runs only once at mount

  // Helper function to format a date string into "HH:MM" format
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  }, []); // No dependencies

  // Determine if the current user is the creator of the offer
  const isOfferCreator = selectedOffer && currentUserId ? currentUserId === selectedOffer.userId : false;
  
  // Determine if the current user is the creator of the related need
  const isNeedCreator = selectedOffer && currentUserId ? currentUserId === selectedOffer.need.userId : false;

  // Return all the necessary data and functions
  return {
    offer: selectedOffer,
    isLoading,
    error,
    currentUserId,
    isOfferCreator,
    isNeedCreator,
    formatDate,
  };
};
