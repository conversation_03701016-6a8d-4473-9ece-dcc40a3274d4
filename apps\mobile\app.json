{"expo": {"name": "", "jsEngine": "jsc", "slug": "mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/needIt.webp", "scheme": "needit", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/needIt.webp"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}}}