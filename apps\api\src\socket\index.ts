import { Server } from 'socket.io';

import type { Server as HTTPServer } from 'node:http';

import type { ClientToServerEvents, ServerToClientEvents } from '@needit/socket';

export let socket: Server<ClientToServerEvents, ServerToClientEvents>;

export function initSocket(httpServer: HTTPServer) {
  socket = new Server<ClientToServerEvents, ServerToClientEvents>(httpServer, {
    /* options */
  });
}

