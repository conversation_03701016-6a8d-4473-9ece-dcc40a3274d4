import { useState } from 'react';
import { <PERSON>, useRouter } from 'expo-router';

import {
  checkEmailExists,
  checkUserHasCredentials,
  credentialsSchema,
  CredentialsSchema,
  CredentialsSignUpForm,
  SocialSignIn,
  useSignUpStore,
} from '@/pages/auth';
import {
  Button,
  ButtonText,
  Center,
  Heading,
  Text,
  TextDivider,
  VStack,
} from '@/shared/ui';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

export default function SignUpCredentialsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { credentials, setCredentials, setSocialAccount } = useSignUpStore();

  const form = useForm<CredentialsSchema>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: credentials,
  });

  const handleNext = async (data: CredentialsSchema) => {
    try {
      setIsLoading(true);

      // Save the data to store
      setCredentials(data);

      // Check if user exists
      const userExists = await checkEmailExists(data.email);

      if (userExists) {
        // Check if the user has credentials (password set up)
        const hasCredentials = await checkUserHasCredentials(data.email);

        if (hasCredentials) {
          // User exists with password already set
          form.setError('email', {
            message: 'An account with this email already exists',
          });
          setIsLoading(false);
          return;
        } else {
          // User exists but without password (social login)
          // Set flag in store that this is a social account
          setSocialAccount(true);

          // Navigate directly to OTP screen
          router.push('/sign-up/otp');
          return;
        }
      }

      // User doesn't exist, continue to profile page
      router.push('/sign-up/profile');
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center className="h-full p-6 -mt-6 bg-background-0">
      <VStack space="lg" className="mx-6 w-full">
        <VStack space="md" className="min-w-full">
          <Heading size="3xl" className="text-typography-900">
            Sign up
          </Heading>

          <Text className="text-typography-600 mb-4">
            Create your account to get started
          </Text>

          <CredentialsSignUpForm form={form} />

          <Button
            className="bg-blue-600 mt-4"
            onPress={form.handleSubmit(handleNext)}
            size="xl"
            isDisabled={isLoading}
          >
            <ButtonText className="text-typography-0">
              {isLoading ? 'Loading...' : 'Continue'}
            </ButtonText>
          </Button>

          <Text className="text-center text-typography-500 mt-2">
            Already have an account?{' '}
            <Link href="/sign-in" asChild>
              <Text className="text-blue-600">Sign in</Text>
            </Link>
          </Text>
        </VStack>

        <TextDivider>OR</TextDivider>

        <SocialSignIn />
      </VStack>
    </Center>
  );
}
