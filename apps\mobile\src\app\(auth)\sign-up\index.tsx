import { useState } from 'react';
import { <PERSON>, useRouter } from 'expo-router';

import {
  credentialsSchema,
  CredentialsSchema,
  CredentialsSignUpForm,
  SocialSignIn,
  useSignUpStore,
  useCheckEmailExists,
  useCheckUserHasCredentials,
} from '@/pages/auth';

import { HttpError } from '@/shared/lib';
import {
  Button,
  ButtonText,
  Center,
  Heading,
  Text,
  TextDivider,
  VStack,
} from '@/shared/ui';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

export default function SignUpCredentialsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { credentials, setCredentials, setSocialAccount } = useSignUpStore();

  // React Query hooks
  const checkEmailExistsMutation = useCheckEmailExists();
  const checkUserHasCredentialsMutation = useCheckUserHasCredentials();

  const form = useForm<CredentialsSchema>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: credentials,
  });

  const handleNext = async (data: CredentialsSchema) => {
    try {
      setIsLoading(true);

      // Save the data to store
      setCredentials(data);

      // Check if user exists
      try {
        const userExists = await checkEmailExistsMutation.mutateAsync(
          data.email
        );

        if (userExists) {
          // Check if the user has credentials (password set up)
          try {
            const hasCredentials =
              await checkUserHasCredentialsMutation.mutateAsync(data.email);

            if (hasCredentials) {
              // User exists with password already set
              form.setError('email', {
                message: 'An account with this email already exists',
              });
              return;
            } else {
              // User exists but without password (social login)
              // Set flag in store that this is a social account
              setSocialAccount(true);

              // Navigate directly to OTP screen
              router.push('/sign-up/otp');
              return;
            }
          } catch (credentialsError) {
            switch (credentialsError.constructor) {
              case HttpError:
                console.error(
                  'HTTP Error checking credentials:',
                  credentialsError.message
                );
                break;
              case SyntaxError:
                console.error(
                  'JSON parsing error checking credentials:',
                  credentialsError.message
                );
                break;
              default:
                console.error(
                  'Unknown error checking credentials:',
                  credentialsError
                );
            }
            return;
          }
        }

        // User doesn't exist, continue to profile page
        router.push('/sign-up/profile');
      } catch (emailError) {
        switch (emailError.constructor) {
          case HttpError:
            console.error('HTTP Error checking email:', emailError.message);
            break;
          default:
            console.error('Unknown error checking email:', emailError);
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center className="h-full p-6 -mt-6 bg-background-0">
      <VStack space="lg" className="mx-6 w-full">
        <VStack space="md" className="min-w-full">
          <Heading size="3xl" className="text-typography-900">
            Sign up
          </Heading>

          <Text className="text-typography-600 mb-4">
            Create your account to get started
          </Text>

          <CredentialsSignUpForm form={form} />

          <Button
            className="bg-blue-600 mt-4"
            onPress={form.handleSubmit(handleNext)}
            size="xl"
            isDisabled={isLoading}
          >
            <ButtonText className="text-typography-0">
              {isLoading ? 'Loading...' : 'Continue'}
            </ButtonText>
          </Button>

          <Text className="text-center text-typography-500 mt-2">
            Already have an account?{' '}
            <Link href="/sign-in" asChild>
              <Text className="text-blue-600">Sign in</Text>
            </Link>
          </Text>
        </VStack>

        <TextDivider>OR</TextDivider>

        <SocialSignIn />
      </VStack>
    </Center>
  );
}
