import type { Need } from '@/pages/needs';

import { nativeFetch } from '@/shared/lib';

export async function getAllNeeds() {
  const response = await nativeFetch('/api/needs', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch needs');
  }

  const data = await response.json();
  return data.needs as Need[];
}
