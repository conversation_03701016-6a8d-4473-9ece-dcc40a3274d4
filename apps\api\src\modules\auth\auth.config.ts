import { env } from '@/shared/config';
import type { Duration } from '@/shared/model';
import type { CookieOptions } from 'hono/utils/cookie';

export const defaultSessionDuration: Duration = '30d';

export const sessionTokenCookieName = 'connect.sid';

export const sessionTokenCookieOptions: CookieOptions = {
  httpOnly: true,
  path: '/',
  sameSite: 'lax',
  secure: env.NODE_ENV === 'production',
};
