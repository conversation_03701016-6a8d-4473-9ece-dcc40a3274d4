import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';

import status from 'http-status';

import { requireAuthMiddleware } from '@/shared/middlewares';
import type { AuthVariables } from '@/shared/model';

import { mediaRepository } from './media.repository';

export const mediaRoutes = new Hono<{
  Variables: AuthVariables;
}>();

// Upload media file - No auth middleware here
mediaRoutes.post('/', async (c) => {
  try {
    const formData = await c.req.parseBody();
    console.log(formData);
    // const file = formData.get('file');

    // console.log(file);

    // if (!file || !(file instanceof File)) {
    //   throw new HTTPException(status.BAD_REQUEST, {
    //     res: Response.json({
    //       error: {
    //         code: status[`${status.BAD_REQUEST}_NAME`],
    //         message: 'No file provided or invalid file type',
    //       },
    //     }),
    //   });
    // }

    // const buffer = Buffer.from(await file.arrayBuffer());
    // const fileName = file.name;
    // const contentType = file.type;
    // // Generate temporary imageRef using UUID
    // const tempImageRef = `temp/${uuidv4()}_${fileName}`;

    // const result = await mediaRepository.uploadMedia(
    //   '', // Pass empty string for userId, it will be replaced in the repository
    //   buffer,
    //   tempImageRef, // Use temporary imageRef
    //   contentType
    // );

    // return c.json({
    //   imageRef: result.imageRef, // Return temporary imageRef
    //   url: result.url,
    // });
    return c.json({});
  } catch (error) {
    console.error('Error uploading media:', error);

    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message:
            error instanceof Error ? error.message : 'Failed to upload media',
        },
      }),
    });
  }
});

// Apply auth middleware to GET and DELETE routes
mediaRoutes.use('*', requireAuthMiddleware);

// Get media URL
mediaRoutes.get('/:imageRef', async (c) => {
  try {
    const imageRef = c.req.param('imageRef');
    const url = await mediaRepository.getMediaUrl(imageRef);
    return c.json({ url });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message:
            error instanceof Error ? error.message : 'Failed to get media URL',
        },
      }),
    });
  }
});

// Delete media
mediaRoutes.delete('/:imageRef', async (c) => {
  try {
    const session = c.get('session')!;
    const imageRef = c.req.param('imageRef');

    // Security check: ensure the user can only delete their own files
    if (!imageRef.startsWith(session.user.id + '/')) {
      throw new HTTPException(status.FORBIDDEN, {
        res: Response.json({
          error: {
            code: status[`${status.FORBIDDEN}_NAME`],
            message: 'You do not have permission to delete this file',
          },
        }),
      });
    }

    await mediaRepository.deleteMedia(imageRef);
    return c.json({ success: true });
  } catch (error) {
    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message:
            error instanceof Error ? error.message : 'Failed to delete media',
        },
      }),
    });
  }
});
