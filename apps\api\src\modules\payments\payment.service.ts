import { env } from '@/shared/config';
import { stripe } from '@/shared/lib';

import { paymentRepository } from './payment.repository';
import type {
  PaymentCreateDto,
  PaymentSelectDto,
  PaymentUpdateDto,
} from './payment.dto';

export const paymentService = {
  async createConnectAccount(email: string) {
    const account = await stripe.accounts.create({
      type: 'express',
      email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });

    const link = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${env.WEB_CLIENT_URL}/reauth`,
      return_url: `${env.WEB_CLIENT_URL}/return`,
      type: 'account_onboarding',
    });

    return { url: link.url };
  },

  async createCheckoutSession(priceId: string) {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{ price: priceId, quantity: 1 }],
      mode: 'subscription',
      success_url: `${env.WEB_CLIENT_URL}/success`,
      cancel_url: `${env.WEB_CLIENT_URL}/cancel`,
    });

    return { url: session.url };
  },

  async createPaymentIntent(amount: number, connectedAccountId: string) {
    const intent = await stripe.paymentIntents.create(
      {
        amount,
        currency: 'eur',
        payment_method_types: ['card'],
      },
      {
        stripeAccount: connectedAccountId,
      }
    );

    return { client_secret: intent.client_secret };
  },

  // Méthodes pour interagir avec le repository
  async savePaymentRecord(data: PaymentCreateDto): Promise<PaymentSelectDto> {
    return await paymentRepository.createPayment(data);
  },

  async getPaymentById(id: string): Promise<PaymentSelectDto | undefined> {
    return await paymentRepository.findById(id);
  },

  async getPaymentsByUserId(userId: string): Promise<PaymentSelectDto[]> {
    return await paymentRepository.findByUserId(userId);
  },

  async updatePayment(
    id: string,
    data: PaymentUpdateDto
  ): Promise<PaymentSelectDto | undefined> {
    return await paymentRepository.updateById(id, data);
  },
};
