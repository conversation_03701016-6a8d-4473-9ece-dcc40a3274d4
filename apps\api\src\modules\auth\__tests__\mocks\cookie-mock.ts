import { vi } from 'vitest';

// Mock for Hono cookie functions
export const mockCookieFunctions = () => {
  vi.mock('hono/cookie', async () => {
    const actual = await vi.importActual('hono/cookie');
    return {
      ...actual,
      setCookie: vi.fn().mockImplementation((c, key, value, options) => {
        // Simulate adding a cookie to the response header
        const cookieValue = `${key}=${value}; Path=${options?.path || '/'}`;
        
        // Add the cookie to the response header
        if (!c.res.headers) {
          c.res.headers = new Headers();
        }
        
        c.res.headers.append('Set-Cookie', cookieValue);
        return c;
      }),
      deleteCookie: vi.fn().mockImplementation((c, key) => {
        // Simulate deleting a cookie
        const cookieValue = `${key}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        
        // Add the expired cookie to the response header
        if (!c.res.headers) {
          c.res.headers = new Headers();
        }
        
        c.res.headers.append('Set-Cookie', cookieValue);
        return c;
      }),
      getCookie: vi.fn().mockImplementation((_c, _key) => {
        // Simulate retrieving a cookie
        // For tests, we can return a dummy value or null
        return null;
      }),
    };
  });
};

// Function to restore mocks after tests
export const restoreCookieMocks = () => {
  vi.restoreAllMocks();
};
