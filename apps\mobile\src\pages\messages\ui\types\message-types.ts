// Base interface with common properties and type discriminant
export interface MessageBubbleProps {
  id: string;
  timestamp: string;
  sender: 'user' | 'other';
  type: 'regular' | 'proposal' | 'accepted' | 'declined';
}

// Regular message
export interface RegularMessageProps extends MessageBubbleProps {
  type: 'regular';
  text: string;
}

// Meeting proposal
export interface MeetingProposalProps extends MessageBubbleProps {
  type: 'proposal';
  meetingTime: string;
  onAccept?: (messageId: string) => void;
  onDecline?: (messageId: string) => void;
}

// Accepted meeting
export interface AcceptedMeetingProps extends MessageBubbleProps {
  type: 'accepted';
  meetingTime: string;
  meetingId: string;
  userRole: 'need_creator' | 'offer_creator';
}

// Declined meeting
export interface DeclinedMeetingProps extends MessageBubbleProps {
  type: 'declined';
  meetingTime: string;
}
