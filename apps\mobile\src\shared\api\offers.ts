import { nativeFetch } from '@/shared/lib';

export type Offer = {
  id: string;
  needId: string;
  userId: string;
  price: number;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
  updatedAt?: string;
  need: {
    id: string;
    title: string;
    description: string;
    userId: string;
    categoryId: string;
    location: {
      x: number;
      y: number;
    };
    createdAt: string;
  };
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    imageRef?: string;
  };
};

export type OfferDetail = {
  id: string;
  needId: string;
  userId: string;
  price: number;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
  updatedAt?: string;
  need: {
    id: string;
    title: string;
    description: string;
    userId: string;
    categoryId: string;
    location: {
      x: number;
      y: number;
    };
    createdAt: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber?: string;
      imageRef?: string;
    };
  };
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    imageRef?: string;
  };
  latestMessage: {
    id: string;
    offerId: string;
    senderId: string;
    content: string;
    status: string;
    createdAt: string;
    updatedAt?: string;
  };
};

export type CreateOfferDto = {
  needId: string;
  price: number;
  message: string;
};

export type UpdateOfferDto = {
  price?: number;
  status?: 'pending' | 'accepted' | 'rejected';
};

export async function getIncomingOffers(): Promise<Offer[]> {
  const response = await nativeFetch('/api/offers/incoming', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch incoming offers');
  }

  const data = await response.json();
  return data.offers as Offer[];
}

export async function getOutgoingOffers(): Promise<Offer[]> {
  const response = await nativeFetch('/api/offers/outgoing', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch outgoing offers');
  }

  const data = await response.json();
  return data.offers as Offer[];
}

export async function getOfferById(offerId: string): Promise<OfferDetail> {
  const response = await nativeFetch(`/api/offers/${offerId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch offer detail');
  }

  const data = await response.json();
  return data.offer as OfferDetail;
}

export async function createOffer(offer: CreateOfferDto) {
  const response = await nativeFetch('/api/offers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...offer,
      price: offer.price.toString(),
    }),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not create offer');
  }

  const data = await response.json();
  return data.offer;
}

export async function updateOffer(offerId: string, data: UpdateOfferDto): Promise<OfferDetail> {
  const response = await nativeFetch(`/api/offers/${offerId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not update offer');
  }

  const responseData = await response.json();
  return responseData.offer as OfferDetail;
}
