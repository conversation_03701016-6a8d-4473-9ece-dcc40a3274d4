import * as Location from 'expo-location';
import { Redirect, useRouter, useFocusEffect } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';

import { useAuth, useNeedStore, useSecureEnv } from '@/shared/model';
import {Button,  ButtonText, MapView, Marker } from '@/shared/ui';
import { Avatar } from '@/shared/ui/gluestack/avatar';

export default function Index() {
  const router = useRouter();
  const { needs, fetchNeeds, isLoading: isLoadingNeeds } = useNeedStore();
  const [locationError, setLocationError] = useState<string | null>(null);
  const [view, setView] = useState<'map' | 'list'>('map');

  const { value: googleMapsApiKey, isLoadingSecureEnv } = useSecureEnv(
    'EXPO_PUBLIC_ENCRYPTED_GOOGLE_MAPS_API_KEY'
  );

  const [region, setRegion] = useState({
    latitude: 49.03985836191832,
    longitude: 2.0781613024585637,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  const getLocation = useCallback(async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        // Use a timeout to prevent hanging
        const locationPromise = Location.getCurrentPositionAsync({});
        const timeoutPromise = new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Location timeout')), 5000)
        );

        const location: Location.LocationObject = await Promise.race([
          locationPromise,
          timeoutPromise,
        ]).catch((err) => {
          console.log('Location timeout, using default location');
          return {
            coords: {
              latitude: 49.03985836191832,
              longitude: 2.0781613024585637,
            },
          } as Location.LocationObject;
        });

        setRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });
        setLocationError(null);
      } else {
        console.log('Location permission was denied');
        setLocationError('Location permission denied');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setLocationError('Failed to get location');
    }
  }, []);

  // Initial data loading
  useEffect(() => {
    fetchNeeds();
    getLocation();
  }, [fetchNeeds, getLocation]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Map screen focused - refreshing data');
      fetchNeeds();
      getLocation();

      return () => {
        // Cleanup if needed
      };
    }, [fetchNeeds, getLocation])
  );

  const { session } = useAuth();

  if (!session) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  if (isLoadingSecureEnv || isLoadingNeeds) {
    return <Text>Loading...</Text>;
  }
  

  // Force re-render when needs change
  const mapKey = `map-${needs.length}-${Date.now()}`;

  // Create markers separately to ensure they're properly rendered
  
  const getCategoryStyle = (category: string) => {
    switch (category) {
      case 'alimentaire':
        return { color: '#fff', backgroundColor: '#green', icon: '🍓' };
      case 'animaux':
        return { color: '#fff', backgroundColor: '#A259FF', icon: '🐾' };
      
      default:
        return { color: '#fff', backgroundColor: '#409BFF', icon: '⚡' };
    }
  };

  const needMarkers = needs.map((need) => {
    console.log(
      `Rendering marker for need: ${need.id} at ${need.location.x}, ${need.location.y}`
    );

    const { icon, backgroundColor, color } = getCategoryStyle(need.categoryId);

    return (
      <Marker
        key={need.id}
        coordinate={{
          latitude: need.location.x,
          longitude: need.location.y,
        }}
        onPress={() => router.push(`/needs/${need.id}`)}
      >
        <View style={[styles.markerWrapper, { backgroundColor }]}>
          <Text style={[styles.markerIcon, { color }]}>{icon}</Text>
        </View>
      </Marker>
    );
  });
  return (
    <View style={styles.container}>
      {view === 'map' ? (
        <>
         <MapView
            key={mapKey}
            style={styles.map}
            initialRegion={region}
            provider="google"
            // @ts-ignore
            googleMapsApiKey={googleMapsApiKey}
            onMapReady={() => console.log('Map is ready')}
            onRegionChangeComplete={(newRegion) =>
              console.log('Region changed', newRegion)
            }
          >
            <Marker
              coordinate={{
                latitude: region.latitude,
                longitude: region.longitude,
              }}
              pinColor="red"
              title="Your location"
            />
            {needMarkers}
          </MapView>

          <View style={styles.debugContainer}>
            <Text style={styles.debugText}>Needs: {needs.length}</Text>
          </View>

          <View style={{ position: 'absolute', bottom: 100, alignSelf: 'center' }}>
            <Button style={{ backgroundColor: '#409BFF', borderRadius: 10 }} onPress={() => setView('list')}>
              <ButtonText>Voir la liste</ButtonText>
            </Button>
          </View>

        </>
      ) : (
        <>
    
    <ScrollView contentContainerStyle={styles.listContainer}>
  {needs.map((need) => {
    const { icon, backgroundColor } = getCategoryStyle(need.categoryId);
    
    return (
      <View key={need.id} style={styles.cardRow}>
        <View style={[styles.categoryIconWrapper, { backgroundColor }]}>
          <Text style={styles.categoryIcon}>{icon}</Text>
        </View>

        <View style={styles.cardText}>
          <Text style={styles.cardTitle}>{need.title}</Text>
          <Text style={styles.cardDescription}>{need.description}</Text>
        </View>

        <Avatar
          size="sm"
          style={styles.avatar}
          
          //onPress={() => router.push(`/profile/${need.userId}`)} // adapt this route to your app
        />
      </View>
    );
  })}
</ScrollView>
  
          <View style={{ position: 'absolute', bottom: 90, alignSelf: 'center', }}>
            <Button style={{backgroundColor:'#409BFF',borderRadius:10}} onPress={() => setView('map')}>
            <ButtonText>Voir la carte</ButtonText>
          </Button>
           </View>

        </>
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },

  map: {
    width: '100%',
    height: '100%',
  },

  listContainer: {
    padding: 16,
    paddingBottom: 120,
  },

  card: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 5,
  },

  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTextWrapper: {
    marginLeft: 12,
    flex: 1,
  },

  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
    color: '#333',
  },

  cardDescription: {
    fontSize: 14,
    color: '#666',
  },

  debugContainer: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    padding: 10,
    borderRadius: 5,
  },

  debugText: {
    fontSize: 12,
    color: '#000',
  },

  listItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f2f2f2',
  },

  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
  },

  markerWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  markerIcon: {
    fontSize: 20,
    fontWeight: 'bold',
  },cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  
  categoryIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  
  categoryIcon: {
    fontSize: 20,
    color: '#fff',
    fontWeight: 'bold',
  },
  
  cardText: {
    flex: 1,
    justifyContent: 'center',
  },
  
  avatar: {
    marginLeft: 12,
    
  },
});