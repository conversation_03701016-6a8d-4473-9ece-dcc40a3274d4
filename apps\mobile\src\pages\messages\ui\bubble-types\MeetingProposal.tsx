import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Text } from '@/shared/ui';
import { MeetingProposalProps } from '../types/message-types';

function MeetingProposal(props: MeetingProposalProps) {
  const { 
    id, 
    sender, 
    timestamp, 
    meetingTime, 
    onAccept, 
    onDecline 
  } = props;
  
  // Handle meeting proposal from current user
  if (sender === 'user') {
    return (
      <View className="mb-3 self-end max-w-[85%]">
        {/* Meeting Proposal Block with light blue background */}
        <View className="rounded-lg overflow-hidden bg-blue-50 border border-blue-100">
          {/* Header with blue background */}
          <View className="bg-blue-600 px-3 py-2 flex-row justify-between items-center">
            <Text className="text-white">Today</Text>
            <Text className="text-white">  {meetingTime || 'Time not set'}</Text>
          </View>

          {/* Meeting proposal text */}
          <View className="px-3 py-2">
            <Text className="text-blue-600 text-center">
              Meeting proposal
            </Text>
          </View>
        </View>
        <Text className="text-xs text-gray-400 mt-1 text-right mr-1">
          {timestamp}
        </Text>
      </View>
    );
  }

  // Handle meeting proposal from other user
  return (
    <View className="mb-3 self-center max-w-[85%]">
      {/* Meeting Proposal Block with light blue background */}
      <View className="rounded-lg overflow-hidden bg-blue-50 border border-blue-100">
        {/* Header with blue background */}
        <View className="bg-blue-600 px-3 py-2 flex-row justify-between items-center">
          <Text className="text-white">Today</Text>
          <Text className="text-white">  {meetingTime || 'Time not set'}</Text>
        </View>

        {/* Meeting proposal text */}
        <View className="px-3 py-2">
          <Text className="text-blue-600 text-center">
            Meeting proposal
          </Text>
        </View>

        {/* Action buttons */}
        <View className="flex-row border-t border-blue-100">
          <TouchableOpacity
            className="flex-1 py-2 items-center"
            onPress={() => {
              if (onAccept && id) {
                onAccept(id);
              }
            }}
          >
            <Text className="text-blue-600 font-medium">Accept</Text>
          </TouchableOpacity>
          <View className="w-px bg-blue-100" />
          <TouchableOpacity
            className="flex-1 py-2 items-center"
            onPress={() => {
              if (onDecline && id) {
                onDecline(id);
              }
            }}
          >
            <Text className="text-red-500 font-medium">Decline</Text>
          </TouchableOpacity>
        </View>
      </View>
      <Text className="text-xs text-gray-400 mt-1 text-center">
        {timestamp}
      </Text>
    </View>
  );
}

export default MeetingProposal;
