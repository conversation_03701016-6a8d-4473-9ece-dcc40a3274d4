import { eq, getTableColumns } from 'drizzle-orm';

import { db, schema } from '@needit/db';

import type { UserSelectDto } from '@/modules/users';

import type { NeedCreateDto, NeedSelectDto, NeedUpdateDto } from './need.dto';

export const needRepository = {
  async create(data: NeedCreateDto) {
    return await db
      .insert(schema.needs)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findAll() {
    return await db.select().from(schema.needs);
  },

  async findByUserId(userId: UserSelectDto['id']) {
    return await db
      .select({
        ...getTableColumns(schema.needs),
        user: getTableColumns(schema.users),
      })
      .from(schema.needs)
      .leftJoin(schema.users, eq(schema.needs.userId, schema.users.id))
      .where(eq(schema.needs.userId, userId));
  },

  async findById(id: NeedSelectDto['id']) {
    return await db
      .select({
        ...getTableColumns(schema.needs),
        user: getTableColumns(schema.users),
      })
      .from(schema.needs)
      .leftJoin(schema.users, eq(schema.needs.userId, schema.users.id))
      .where(eq(schema.needs.id, id))
      .then((res) => res[0]);
  },

  async updateById(id: NeedSelectDto['id'], data: NeedUpdateDto) {
    return await db
      .update(schema.needs)
      .set(data)
      .where(eq(schema.needs.id, id))
      .returning()
      .then((res) => res[0]);
  },

  async deleteById(id: NeedSelectDto['id']) {
    return await db
      .delete(schema.needs)
      .where(eq(schema.needs.id, id))
      .returning()
      .then((res) => res[0]);
  },
};
