export type Result<T, E = Error> =
  | {
      success: true;
      data: T;
    }
  | {
      success: false;
      error: E;
    };

export async function catchError<T>(
  promise: Promise<T>
): Promise<Result<T, Error>> {
  return promise
    .then((data): Result<T, Error> => ({ success: true, data }))
    .catch((error): Result<T, Error> => ({ success: false, error }));
}

export async function catchTypedError<T, E extends new (...args: any) => Error>(
  promise: Promise<T>,
  errorsToCatch?: E[]
): Promise<Result<T, InstanceType<E>>> {
  return promise
    .then((data): Result<T, InstanceType<E>> => ({ success: true, data }))
    .catch((error): Result<T, InstanceType<E>> => {
      if (!errorsToCatch) {
        // display error type in console
        console.error(error);
        return { success: false, error };
      }

      if (errorsToCatch.some((errorType) => error instanceof errorType)) {
        return { success: false, error };
      }

      console.error(error);
      throw error;
    });
}
