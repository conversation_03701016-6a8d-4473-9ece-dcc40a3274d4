import { View } from 'react-native';
import { useState } from 'react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  CredentialsSchema,
  credentialsSchema,
  CredentialsSignUpForm,
  otpSchema,
  OtpSchema,
  OTPForm,
  profileSchema,
  ProfileSchema,
  ProfileSignUpForm,
  SocialSignIn,
} from '@/pages/auth';

import { markTodo, nativeFetch } from '@/shared/lib';
import {
  Button,
  ButtonText,
  Heading,
  HStack,
  TextDivider,
  VStack,
} from '@/shared/ui';
import { useMultistepForm } from '@/shared/model';

type FormData = CredentialsSchema & ProfileSchema & OtpSchema;

const INITIAL_DATA: FormData = {
  email: '',
  password: '',
  confirmPassword: '',
  imageUri: '',
  firstName: '',
  lastName: '',
  code: '',
};

export function SignUpForm() {
  const [data, setData] = useState(INITIAL_DATA);
  const updateData = (fields: Partial<FormData>) => {
    setData((prev) => {
      return { ...prev, ...fields };
    });
  };

  const credentialsForm = useForm<CredentialsSchema>({
    resolver: zodResolver(credentialsSchema),
  });

  const profileForm = useForm<ProfileSchema>({
    resolver: zodResolver(profileSchema),
  });

  const otpForm = useForm<OtpSchema>({
    resolver: zodResolver(otpSchema),
  });

  const multistepForm = useMultistepForm(
    [
      <CredentialsSignUpForm form={credentialsForm} />,
      <ProfileSignUpForm
        form={profileForm}
        onPickImage={() => {
          // TODO: Implement image picker
          markTodo('Implement image picker');
        }}
        onTakePhoto={() => {
          // TODO: Implement photo taking
          markTodo('Implement photo taking');
        }}
      />,
      <OTPForm form={otpForm} />,
    ],
    [credentialsForm, profileForm, otpForm]
  );

  const handleNextStep = async () => {
    const { currentForm } = multistepForm;

    const values = currentForm.getValues();
    updateData(values);

    switch (currentForm) {
      case credentialsForm: {
        const { email } = credentialsForm.getValues();

        const [error, response] = await nativeFetch<{ user: { id: string } }>(
          `/api/users?email=${email}`,
          {
            method: 'GET',
            credentials: 'include',
          }
        );

        if (error) {
          return;
        }

        const data = await response.json();
        const user = data.user;

        const credentialsResponse = await nativeFetch(
          `/api/users/${user.id}/credentials`,
          {
            method: 'GET',
            credentials: 'include',
          }
        ).catch((error) => console.error(error));

        if (credentialsResponse?.ok) {
          currentForm.setError('email', {
            message: 'This email is already in use',
          });
          return;
        }

        multistepForm.goToStep(2);
        return;
      }
      case profileForm:
        break;
      case otpForm:
        break;
      default:
        break;
    }

    if (multistepForm.isLastStep) {
      console.log(data);
      return;
    }

    multistepForm.nextStep();
  };

  return (
    <VStack space="lg" className="mx-6">
      <VStack space="md" className="min-w-full">
        <Heading size="3xl" className="text-typography-900">
          Sign up
        </Heading>
        {multistepForm.currentStep}
        <HStack space="md">
          {!multistepForm.isFirstStep && (
            <Button
              onPress={multistepForm.previeousStep}
              size="xl"
              className="flex-1"
            >
              <ButtonText className="text-typography-0">Back</ButtonText>
            </Button>
          )}
          <Button
            className="bg-blue-600 flex-auto"
            onPress={multistepForm.currentForm.handleSubmit(handleNextStep)}
            size="xl"
          >
            <ButtonText className="text-typography-0">
              {multistepForm.isFirstStep
                ? 'Sign up with Email'
                : multistepForm.isLastStep
                ? 'Submit'
                : 'Continue'}
            </ButtonText>
          </Button>
        </HStack>
      </VStack>
      <View className="mx-6">
        <TextDivider>OR</TextDivider>
      </View>
      <SocialSignIn />
    </VStack>
  );
}
