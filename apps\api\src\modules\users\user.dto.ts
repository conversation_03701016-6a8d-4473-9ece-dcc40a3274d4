import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod';

import { schema } from '@needit/db';

export const userCreateDto = createInsertSchema(schema.users).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type UserCreateDto = z.infer<typeof userCreateDto>;

export const userSelectDto = createSelectSchema(schema.users);
export type UserSelectDto = z.infer<typeof userSelectDto>;

export const userUpdateDto = createUpdateSchema(schema.users)
  // remove the id field from the update schema
  .omit({ id: true, createdAt: true, updatedAt: true });
export type UserUpdateDto = z.infer<typeof userUpdateDto>;
