export type CatchErrorResult<T, E = Error> = [E, undefined] | [undefined, T];

export async function catchError<T>(
  promise: Promise<T>
): Promise<CatchErrorResult<T, Error>> {
  return promise
    .then((data): CatchErrorResult<T, Error> => [undefined, data])
    .catch((error): CatchErrorResult<T, Error> => [error, undefined]);
}

export async function catchTypedError<
  T,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const E extends (new (...args: any) => Error)[]
>(
  promise: Promise<T>,
  errorsToCatch?: E
): Promise<
  CatchErrorResult<
    T,
    E[number] extends new (...args: any) => infer I ? I : Error
  >
> {
  return promise
    .then(
      (
        data
      ): CatchErrorResult<
        T,
        E[number] extends new (...args: any) => infer I ? I : Error
      > => [undefined, data]
    )
    .catch(
      (
        error
      ): Catch<PERSON><PERSON>rResult<
        T,
        E[number] extends new (...args: any) => infer I ? I : Error
      > => {
        if (!errorsToCatch) {
          // display error type in console
          console.error(error);
          return [error, undefined];
        }

        if (errorsToCatch.some((errorType) => error instanceof errorType)) {
          return [error, undefined];
        }

        throw error;
      }
    );
}
