import { useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { type CredentialsSchema } from '@/pages/auth';

import {
  AlertCircleIcon,
  EyeIcon,
  EyeOffIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
} from '@/shared/ui';

interface Props {
  form: UseFormReturn<CredentialsSchema>;
}

export function CredentialsSignUpForm({ form }: Props) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <>
      <Controller
        control={form.control}
        name="email"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText className="text-typography-500">
                Email
              </FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type="text"
                autoCapitalize="none"
                keyboardType="email-address"
                {...field}
                onChangeText={field.onChange}
              />
            </Input>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />
      <Controller
        control={form.control}
        name="password"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText className="text-typography-500">
                Password
              </FormControlLabelText>
            </FormControlLabel>
            <Input className="w-full">
              <InputField
                type={showPassword ? 'text' : 'password'}
                {...field}
                onChangeText={field.onChange}
              />
              <InputSlot
                className="pr-3"
                onPress={() => setShowPassword(!showPassword)}
              >
                <InputIcon as={showPassword ? EyeIcon : EyeOffIcon} />
              </InputSlot>
            </Input>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />
      <Controller
        control={form.control}
        name="confirmPassword"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText className="text-typography-500">
                Confirm Password
              </FormControlLabelText>
            </FormControlLabel>
            <Input className="w-full">
              <InputField
                type={showConfirmPassword ? 'text' : 'password'}
                {...field}
                onChangeText={field.onChange}
              />
              <InputSlot
                className="pr-3"
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <InputIcon as={showConfirmPassword ? EyeIcon : EyeOffIcon} />
              </InputSlot>
            </Input>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />
    </>
  );
}
