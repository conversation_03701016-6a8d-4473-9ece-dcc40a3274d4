import { nativeFetch } from '@/shared/lib';

export type IncomingOffer = {
  id: string;
  needId: string;
  userId: string;
  price: number;
  status: string;
  createdAt: string;
  updatedAt?: string;
  need: {
    id: string;
    title: string;
    description: string;
    userId: string;
    categoryId: string;
    location: {
      x: number;
      y: number;
    };
    createdAt: string;
  };
};

export async function getIncomingOffers() {
  const response = await nativeFetch('/api/offers/incoming', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch incoming offers');
  }

  const data = await response.json();
  return data.offers as IncomingOffer[];
}
