services:
  postgres_test:
    image: postgis/postgis:17-3.5
    restart: always
    environment:
      POSTGRES_USER: needit_test
      POSTGRES_PASSWORD: needit_test
      POSTGRES_DB: needit_test
    logging:
      options:
        max-size: 10m
        max-file: "3"
    ports:
      - "5438:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U needit_test -d needit_test"]
      interval: 5s
      timeout: 5s
      retries: 5
