import { eq } from 'drizzle-orm';

import { db, schema } from '@needit/db';

import type { SessionCreateDto, SessionSelectDto } from '@/modules/auth';
import type { UserSelectDto } from '@/modules/users';

export const sessionRepository = {
  async create(data: SessionCreateDto) {
    return await db
      .insert(schema.sessions)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findByTokenHash(hashedToken: string) {
    return await db
      .select()
      .from(schema.sessions)
      .where(eq(schema.sessions.tokenHash, hashedToken))
      .then((res) => res[0]);
  },

  async updateExpiresAtById(id: SessionSelectDto['id'], expiresAt: Date) {
    return await db
      .update(schema.sessions)
      .set({ expiresAt })
      .where(eq(schema.sessions.id, id))
      .returning()
      .then((res) => res[0]);
  },

  async deleteById(id: SessionSelectDto['id']) {
    return await db
      .delete(schema.sessions)
      .where(eq(schema.sessions.id, id))
      .returning()
      .then((res) => res[0]);
  },

  async deleteByUserId(userId: UserSelectDto['id']) {
    return await db
      .delete(schema.sessions)
      .where(eq(schema.sessions.userId, userId))
      .returning()
      .then((res) => res[0]);
  },
};
