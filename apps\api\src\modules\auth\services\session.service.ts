import { sessionRepository } from '@/modules/auth';
import type { SessionCreateDto, SessionSelectDto } from '@/modules/auth';

export const sessionService = {
  async createSession(sessionData: SessionCreateDto) {
    const newSession = await sessionRepository.create(sessionData);

    return newSession;
  },

  async getSessionByTokenHash(hashedToken: string) {
    const foundSession = await sessionRepository.findByTokenHash(hashedToken);

    return foundSession;
  },

  async updateSessionExpirationDateById(
    id: SessionSelectDto['id'],
    expiresAt: Date
  ) {
    const updatedSession = await sessionRepository.updateExpiresAtById(
      id,
      expiresAt
    );

    return updatedSession;
  },

  async deleteSessionById(id: SessionSelectDto['id']) {
    const deletedSession = await sessionRepository.deleteById(id);

    return deletedSession;
  },
};
