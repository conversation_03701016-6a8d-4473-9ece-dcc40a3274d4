import { db, schema } from '@needit/db';

import { eq } from 'drizzle-orm';

import type { UserCreateDto, UserSelectDto, UserUpdateDto } from './user.dto';

export const userRepository = {
  async create(data: UserCreateDto) {
    return await db
      .insert(schema.users)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findById(id: UserSelectDto['id']) {
    return await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.id, id))
      .then((res) => res[0]);
  },

  async findByEmail(email: string) {
    return await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.email, email))
      .then((res) => res[0]);
  },

  async updateById(id: UserSelectDto['id'], data: UserUpdateDto) {
    return await db
      .update(schema.users)
      .set(data)
      .where(eq(schema.users.id, id))
      .returning()
      .then((res) => res[0]);
  },

  async deleteById(id: UserSelectDto['id']) {
    return await db
      .delete(schema.users)
      .where(eq(schema.users.id, id))
      .returning()
      .then((res) => res[0]);
  },
};
