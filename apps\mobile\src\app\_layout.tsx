import { Stack } from 'expo-router';

import './global.css';
import {
  GluestackUIProvider,
  ReactQueryProvider,
  SocketProvider,
} from './_providers';

export default function RootLayout() {
  return (
    <GluestackUIProvider mode="light">
      <ReactQueryProvider>
        <SocketProvider>
          <Stack>
            <Stack.Screen name="(main)" options={{ headerShown: false }} />
            <Stack.Screen name="(auth)" options={{ headerShown: false }} />
          </Stack>
        </SocketProvider>
      </ReactQueryProvider>
    </GluestackUIProvider>
  );
}
