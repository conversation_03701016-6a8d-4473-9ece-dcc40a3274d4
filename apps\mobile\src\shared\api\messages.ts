import { nativeFetch } from '@/shared/lib';

export interface Message {
  id: string;
  offerId: string;
  senderId: string;
  content: string;
  status: 'sent' | 'read' | 'failed';
  createdAt: string;
  updatedAt: string;
  sender?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string; 
  };
}

export async function getMessagesByOfferId(offerId: string): Promise<Message[]> {
  try {
    const response = await nativeFetch(`/api/messages/offer/${offerId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      console.error('Failed to fetch messages, status:', response.status);
      return [];
    }

    const data = await response.json();
    return data.messages as Message[];
  } catch (error) {
    console.error('Error fetching messages:', error);
    return [];
  }
}

export async function sendMessage(offerId: string, content: string): Promise<Message> {
  const response = await nativeFetch('/api/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      offerId,
      content,
    }),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to send message');
  }

  const data = await response.json();
  return data.message as Message;
}

export async function markMessageAsRead(messageId: string): Promise<Message> {
  const response = await nativeFetch(`/api/messages/${messageId}/read`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to mark message as read');
  }

  const data = await response.json();
  return data.message as Message;
}
