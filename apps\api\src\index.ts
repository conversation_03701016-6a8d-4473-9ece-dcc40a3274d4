import { serve } from '@hono/node-server';

import type { Server as HTTPServer } from 'node:http';

import { initSocket, socket } from '@/socket';

import { app } from './app';
import { env } from './shared/config';

const port = env.PORT;
console.log(`Server is running on http://localhost:${port}`);

const httpServer = serve({
  fetch: app.fetch,
  port,
});

initSocket(httpServer as HTTPServer);

socket.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});
