import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  SafeAreaView,
  StyleSheet,
} from 'react-native';

import { useNeedStore } from '@/shared/model';
import { CreateOfferDialog } from '@/pages/needs';

const NeedDetail = () => {
  const { id: needId } = useLocalSearchParams();
  const {
    selectedNeed,
    fetchNeedDetail,
    isLoading: isLoadingNeed,
  } = useNeedStore();

  useEffect(() => {
    if (needId) {
      fetchNeedDetail(needId as string);
    }
  }, [needId, fetchNeedDetail]);

  const dateStringToLocaleString = (dateString: string) => {
    // Display date as "time ago"
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours} h`;
    } else if (minutes > 0) {
      return `${minutes} min`;
    } else {
      return 'just now';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          {/* This line is used to create the back button */}
          <Ionicons name="chevron-back" size={24} color="black" />
          <Text>Back</Text>
        </View>

        <Text style={styles.headerTitle}>{selectedNeed?.title}</Text>
        <View style={styles.headerRight}>
          <Text style={styles.reportText}>Report</Text>
          <Ionicons name="share-outline" size={24} color="#409FFF" />
          {/* This line can be used to report and share information. */}
        </View>
      </View>

      <ScrollView>
        {/* This line is the main image of the request */}
        <Image
          source={{
            uri: 'https://www.ulisse.cnrs.fr/wp-content/uploads/2019/08/colis-preparation-livraison-640x427-1-300x200.jpg',
          }}
          style={styles.mainImage}
        />

        {/* This line provides rapid information on the request */}
        <View style={styles.quickInfo}>
          <View style={styles.infoItem}>
            <Ionicons name="time-outline" size={20} color="green" />
            <Text>
              {isLoadingNeed
                ? 'Loading...'
                : selectedNeed?.createdAt
                ? dateStringToLocaleString(selectedNeed.createdAt)
                : 'Unknown date'}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Ionicons name="location-outline" size={20} color="blue" />
            <Text>Paris</Text>
          </View>
        </View>

        {/* This line provides information on the description of the request */}
        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>Description</Text>
          <Text style={styles.descriptionText}>
            {selectedNeed?.description}
          </Text>
        </View>

        {/* This line provides information about the profile photo and description of the applicant. */}
        <View style={styles.userProfile}>
          <Image
            source={{
              uri: 'https://static.animaute.fr/upload/img/animhote/max/10929412341643236116.webp',
            }}
            style={styles.profilePic}
          />
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{`${
              selectedNeed?.user?.firstName || ''
            } ${selectedNeed?.user?.lastName || ''}`}</Text>
            <View style={styles.rating}>
              <Ionicons name="star" size={16} color="gold" />
              <Text>4.9(10 reviews)</Text>
            </View>
          </View>
          <Text style={styles.viewProfile}>View profile →</Text>
        </View>
      </ScrollView>

      <CreateOfferDialog needId={needId as string} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#409FFF',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportText: {
    color: 'red',
    marginRight: 16,
  },
  mainImage: {
    width: '100%',
    aspectRatio: 16 / 9,
    resizeMode: 'cover',
  },
  quickInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    minWidth: 80,
  },
  description: {
    padding: 16,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  descriptionText: {
    lineHeight: 20,
    color: '#333',
  },
  userProfile: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  profilePic: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontWeight: 'bold',
  },
  userLocation: {
    color: 'gray',
    fontSize: 12,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  viewProfile: {
    color: 'blue',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
});

export default NeedDetail;
