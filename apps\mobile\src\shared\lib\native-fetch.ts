import { Platform } from 'react-native';

import setC<PERSON>ie, { splitCookiesString } from 'set-cookie-parser';

import { backendBaseUrl, sessionCookieName } from '@/shared/config';
import {
  type CatchErrorResult,
  getStoredItem,
  storeCookie,
} from '@/shared/lib';

// Get the parameters of the default fetch function
type NativeFetchParams = Parameters<typeof fetch>;
type FetchSource = 'backend-api' | 'external';

// Extend Response type to include typed json method
// eslint-disable-next-line @typescript-eslint/no-explicit-any
interface TypedResponse<JsonType = any> extends Omit<Response, 'json'> {
  json(): Promise<CatchErrorResult<JsonType, SyntaxError>>;
}

export class HttpError extends Error {
  name = 'HttpError';
  constructor(public cause: Response) {
    super(`HTTP error: ${cause.status} ${cause.statusText}`);
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function nativeFetch<JsonType = any>(
  input: NativeFetchParams[0],
  init: NativeFetchParams[1] & {
    source?: FetchSource;
  }
): Promise<CatchErrorResult<TypedResponse<JsonType>, HttpError | Error>> {
  // If no source is specified, we assume it's a backend API request
  if (init.source === undefined) {
    init.source = 'backend-api';
  }

  // If the source is a backend API request, we prepend the backend base URL
  if (init.source === 'backend-api') {
    input = backendBaseUrl + input;
  }

  if (Platform.OS !== 'web' && init.credentials === 'include') {
    const sessionToken = await getStoredItem(sessionCookieName, {
      secure: true,
    }).then((result) => result[1]);

    if (sessionToken) {
      init.headers = {
        ...init.headers,
        Cookie: `connect.sid=${sessionToken}`,
      };
    }
  }

  let response: Response;
  try {
    response = await fetch(input, init);
  } catch (error) {
    return [error as Error, undefined];
  }

  // If the response is a backend API response, we check for cookies and store them
  if (Platform.OS !== 'web' && init?.credentials === 'include') {
    // Get all the cookies from the response as a single string
    const setCookieHeader = response.headers.get('set-cookie');

    if (setCookieHeader) {
      // Split each cookie string
      const cookieStrings = splitCookiesString(setCookieHeader);

      // Parse each cookie string into a cookie object and store it
      cookieStrings.forEach(async (cookieString) => {
        const cookie = setCookie(cookieString)[0];

        await storeCookie(cookie);
      });
    }
  }

  if (!response.ok) return [new HttpError(response), undefined];

  const typedResponse: TypedResponse<JsonType> = {
    ...response,
    json: async (): Promise<CatchErrorResult<JsonType, SyntaxError>> => {
      try {
        const data = await response.json();
        return [undefined, data];
      } catch (error) {
        return [error as SyntaxError, undefined];
      }
    },
  };

  return [undefined, typedResponse];
}

// Helper functions for cleaner API calls
export async function fetchJson<T>(
  url: string,
  options?: RequestInit & { source?: 'backend-api' | 'external' }
): Promise<CatchErrorResult<T, HttpError | SyntaxError | Error>> {
  const [fetchError, response] = await nativeFetch<T>(url, {
    credentials: 'include',
    ...options,
  });

  if (fetchError) return [fetchError, undefined];

  const [jsonError, data] = await response.json();
  if (jsonError) return [jsonError, undefined];

  return [undefined, data];
}

// Helper for simple HEAD/GET requests
export async function fetchExists(
  url: string
): Promise<CatchErrorResult<boolean, HttpError | Error>> {
  const [error, response] = await nativeFetch(url, {
    method: 'HEAD',
    credentials: 'include',
  });

  if (error) return [error, undefined];
  return [undefined, response.ok];
}
