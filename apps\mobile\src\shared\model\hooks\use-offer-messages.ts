// Import React hooks and API utilities
import { useState, useEffect, useCallback } from 'react';
import { getMessagesByOfferId, sendMessage, Message } from '@/shared/api';

// Interface for the hook's props: it expects an offer ID
interface UseOfferMessagesProps {
  offerId: string;
}

// Interface describing what the hook will return
interface UseOfferMessagesReturn {
  messages: Message[];
  isLoadingMessages: boolean;
  inputMessage: string;
  setInputMessage: (message: string) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  handleSendMessage: () => Promise<Message | undefined>;
  fetchMessages: () => Promise<void>;
}

// Declaration of the custom hook
export const useOfferMessages = ({ offerId }: UseOfferMessagesProps): UseOfferMessagesReturn => {
  // State to store the list of messages
  const [messages, setMessages] = useState<Message[]>([]);
  // State to track whether messages are currently loading
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  // State to store the user's input message
  const [inputMessage, setInputMessage] = useState('');
  // Function to fetch messages related to an offer
  const fetchMessages = useCallback(async () => {
    if (!offerId) return; // If no offerId, do nothing

    setIsLoadingMessages(true); // Start loading
    try {
      const fetchedMessages = await getMessagesByOfferId(offerId); // Call API to get messages
      setMessages(fetchedMessages); // Update state with fetched messages
    } catch (error) {
      console.error('Failed to fetch messages:', error); // Log the error
      setMessages([]); // Clear messages on error
    } finally {
      setIsLoadingMessages(false); // Always stop loading
    }
  }, [offerId]); // This function depends on offerId

  // Function to send a new message
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !offerId) return; // If input is empty or no offerId, do nothing

    try {
      const newMessage = await sendMessage(offerId, inputMessage.trim()); // Send the message to the API

      // Add the new message to the current list
      setMessages((prevMessages) => [...prevMessages, newMessage]);

      // Clear the input after sending
      setInputMessage('');

      return newMessage; // Return the newly sent message
    } catch (error) {
      console.error('Failed to send message:', error); // Log error
      throw error; // Re-throw the error to handle it elsewhere if needed
    }
  };

  // useEffect runs when the component mounts or when offerId changes
  useEffect(() => {
    if (offerId) {
      fetchMessages(); // Automatically fetch messages
    }
  }, [offerId, fetchMessages]); // Dependencies: offerId and fetchMessages

  // Return all states and functions so that components can use them
  return {
    messages,
    isLoadingMessages,
    inputMessage,
    setInputMessage,
    setMessages,
    handleSendMessage,
    fetchMessages,
  };
};
