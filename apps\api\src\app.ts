import { Hono } from 'hono';

import { cors } from 'hono/cors';
import { HTTPException } from 'hono/http-exception';

import status from 'http-status';

import { authRoutes } from '@/modules/auth';
import { categoryRoutes } from '@/modules/categories';
import { envRoutes } from '@/modules/env';
import { mediaRoutes } from '@/modules/medias';
import { messageRoutes } from '@/modules/messages';
import { needRoutes } from '@/modules/needs';
import { offerRoutes } from '@/modules/offers';
import { userRoutes } from '@/modules/users';
import { paymentRoutes } from '@/modules/payments';
import { apiPrefix, mobileClientUrl, webClientUrl } from '@/shared/config';
import { pinoLoggerMiddleware, sessionMiddleware } from '@/shared/middlewares';

const app = new Hono().basePath(apiPrefix);

// middlewares
app.use(
  '*',
  cors({
    origin: [webClientUrl, mobileClientUrl],
    credentials: true,
  })
);

app.use('*', pinoLoggerMiddleware);
app.use('*', sessionMiddleware);

// error handling
app.notFound(async (c) => {
  return c.json(
    {
      error: {
        code: status[`${status.NOT_FOUND}_NAME`],
        message: status[status.NOT_FOUND],
      },
    },
    status.NOT_FOUND
  );
});

app.onError((err, c) => {
  // Handle
  if (err instanceof HTTPException) {
    // Get the custom response
    return err.getResponse();
  }

  // Handle other errors
  return c.json(
    {
      error: {
        code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
        message: err.message || status[status.INTERNAL_SERVER_ERROR],
        type: err.constructor.name,
      },
    },
    status.INTERNAL_SERVER_ERROR
  );
});

// routes
app
  .route('/auth', authRoutes)
  .route('/categories', categoryRoutes)
  .route('/env', envRoutes)
  .route('/medias', mediaRoutes)
  .route('/messages', messageRoutes)
  .route('/needs', needRoutes)
  .route('/offers', offerRoutes)
  .route('/users', userRoutes)
  .route('/payments', paymentRoutes);

export { app };
