import { db, schema } from '@needit/db';
import { eq, getTableColumns } from 'drizzle-orm';
import { jsonBuildObject } from '@/shared/lib';

import type { MessageCreateDto } from './message.dto';

export const messageRepository = {
  async create(data: MessageCreateDto) {
    return await db
      .insert(schema.messages)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findByOfferId(offerId: string) {
    try {
      const messages = await db
        .select({
          ...getTableColumns(schema.messages),
          sender: jsonBuildObject(getTableColumns(schema.users)),
        })
        .from(schema.messages)
        .leftJoin(schema.users, eq(schema.messages.senderId, schema.users.id))
        .where(eq(schema.messages.offerId, offerId))
        .orderBy(schema.messages.createdAt);
      
      return messages;
    } catch (error) {
      console.error('Error finding messages by offerId:', error);
      return []; // Return empty array instead of failing
    }
  },

  async updateStatus(messageId: string, status: 'sent' | 'read' | 'failed') {
    return await db
      .update(schema.messages)
      .set({ status })
      .where(eq(schema.messages.id, messageId))
      .returning()
      .then((res) => res[0]);
  },
};
