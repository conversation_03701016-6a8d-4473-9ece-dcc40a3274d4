import { z } from 'zod';

// OTP request schema
export const otpCreateDto = z.object({
  type: z.enum([
    'email-verification',
    'sign-in',
    'forget-password',
    'update-password',
  ]),
  userId: z.string().uuid().optional(), // Optional for authenticated routes
});

export type OTPCreateDto = z.infer<typeof otpCreateDto>;

// OTP verification schema
export const otpVerifyDto = z.object({
  userId: z.string().uuid(),
  otp: z.string().length(6),
  type: z.enum([
    'email-verification',
    'sign-in',
    'forget-password',
    'update-password',
  ]),
});

export type OTPVerifyDto = z.infer<typeof otpVerifyDto>;
