import { messageRepository } from './message.repository';
import type { MessageCreateDto } from './message.dto';

export const messageService = {
  async sendMessage(data: MessageCreateDto) {
    const message = await messageRepository.create(data);
    return message;
  },

  async getMessagesByOfferId(offerId: string) {
    try {
      const messages = await messageRepository.findByOfferId(offerId);
      return messages;
    } catch (error) {
      console.error('Error getting messages by offerId:', error);
      return []; // Return empty array instead of failing
    }
  },

  async markMessageAsRead(messageId: string) {
    const message = await messageRepository.updateStatus(messageId, 'read');
    return message;
  },
};
