import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod';

import { schema } from '@needit/db';

export const needCreateDto = createInsertSchema(schema.needs).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type NeedCreateDto = z.infer<typeof needCreateDto>;

export const needSelectDto = createSelectSchema(schema.needs);
export type NeedSelectDto = z.infer<typeof needSelectDto>;

export const needUpdateDto = createUpdateSchema(schema.needs)
  // remove the id field from the update schema
  .omit({ id: true, createdAt: true, updatedAt: true });
export type NeedUpdateDto = z.infer<typeof needUpdateDto>;
