import { getCookie, setCookie } from 'hono/cookie';
import { createMiddleware } from 'hono/factory';

import {
  defaultSessionDuration,
  hashSessionToken,
  sessionSelectDto,
  sessionService,
  sessionTokenCookieName,
  sessionTokenCookieOptions,
} from '@/modules/auth';
import { userService } from '@/modules/users';

import { applyDtoSchema, getDateFromNow, parseDuration } from '@/shared/lib';
import type { AuthVariables } from '@/shared/model';

export const sessionMiddleware = createMiddleware<{
  Variables: AuthVariables;
}>(async (c, next) => {
  const sessionToken = getCookie(c, sessionTokenCookieName);

  if (!sessionToken) {
    c.set('session', null);
    return await next();
  }

  const hashedSessionToken = hashSessionToken(sessionToken);

  const session = await sessionService.getSessionByTokenHash(
    hashedSessionToken
  );

  // When the session is not found
  if (!session) {
    c.set('session', null);
    return await next();
  }

  // When the session is expired, delete it
  if (Date.now() >= session.expiresAt.getTime()) {
    await sessionService.deleteSessionById(session.id);
    c.set('session', null);
    return await next();
  }

  // When the session is about to expire in less than 15 days, update the expiration date
  if (Date.now() >= session.expiresAt.getTime() - parseDuration('15d')) {
    const newExpirationDate = getDateFromNow(defaultSessionDuration);
    const updatedSession = await sessionService.updateSessionExpirationDateById(
      session.id,
      newExpirationDate
    );

    setCookie(c, sessionTokenCookieName, sessionToken, {
      ...sessionTokenCookieOptions,
      expires: updatedSession.expiresAt,
    });

    session.expiresAt = updatedSession.expiresAt;
  }

  const user = await userService.getUserById(session.userId);

  const sessionDto = applyDtoSchema(session, sessionSelectDto);

  // Set the session in the context
  c.set('session', { ...sessionDto, user });

  await next();
});
