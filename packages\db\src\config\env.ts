/* eslint-disable node/no-process-env */
import * as dotenv from 'dotenv';

import { z } from 'zod';

dotenv.config();

const EnvSchema = z.object({
  // Database
  DATABASE_URL: z.string().url(),
});

// eslint-disable-next-line ts/no-redeclare
const { data: env, error } = EnvSchema.safeParse(process.env);

if (error) {
  console.error('❌ Invalid env:');
  console.error(JSON.stringify(error.flatten().fieldErrors, null, 2));
  process.exit(1);
}

export default env!;
