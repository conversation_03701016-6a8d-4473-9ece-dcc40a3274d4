// Import React hooks and API utilities
import { useState, useCallback } from 'react';
import { sendMessage, Message } from '@/shared/api';
import { Alert } from 'react-native';

// Interface for the hook's props: expects an offer ID, messages list, and setter
interface UseMeetingProposalsProps {
  offerId: string;
  messages: Message[];
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
}

// Interface describing what the hook will return
interface UseMeetingProposalsReturn {
  handleMakeOffer: () => Promise<void>;
  handleAcceptMeeting: (messageId: string) => void;
  handleDeclineMeeting: (messageId: string) => void;
}

// Declaration of the custom hook
export const useMeetingProposals = ({
  offerId,
  messages,
  setMessages,
}: UseMeetingProposalsProps): UseMeetingProposalsReturn => {
  
  // Function to propose a meeting
  const handleMakeOffer = useCallback(async () => {
    try {
      const currentTime = new Date();
      
      // Schedule the meeting 1 hour from now
      const meetingTime = new Date(currentTime.getTime() + 60 * 60 * 1000);
      
      // Format the meeting time nicely (e.g., 14:30)
      const formattedTime = meetingTime.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      // Create the meeting proposal content
      const meetingProposalContent = JSON.stringify({
        type: 'meetingProposal',
        time: formattedTime
      });
      
      // Send the meeting proposal as a message
      const newMessage = await sendMessage(offerId, meetingProposalContent);
      
      // Add the proposal message to the message list, marking it as a meeting proposal
      setMessages(prevMessages => {
        const updatedMessage = {
          ...newMessage,
          isMeetingProposal: true,
          meetingTime: formattedTime
        };
        return [...prevMessages, updatedMessage];
      });
      
      // Show confirmation to the user
      Alert.alert(
        'Meeting Proposal Sent',
        'Your meeting proposal has been sent.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error in handleMakeOffer:', error);
      Alert.alert('Error', 'An error occurred while creating the meeting proposal.');
    }
  }, [offerId, setMessages]); // Depends on offerId and setMessages

  // Function to accept a meeting proposal
  const handleAcceptMeeting = useCallback((messageId: string) => {
    // Find the meeting proposal by its ID
    const proposalMessage = messages.find(msg => msg.id === messageId);
    
    if (!proposalMessage) {
      console.error('Could not find meeting proposal message with ID:', messageId);
      Alert.alert('Error', 'Could not find the meeting proposal.');
      return;
    }
    
    // Mark the meeting proposal as accepted
    setMessages(prevMessages => 
      prevMessages.map(msg => {
        if (msg.id === messageId) {
          return {
            ...msg,
            isAccepted: true
          };
        }
        return msg;
      })
    );
    
    try {
      // Here you would typically update the server to confirm acceptance
      console.log('Would update server with acceptance for message:', messageId);
    } catch (error) {
      console.error('Error updating server:', error);
    }
    
    // Show confirmation to the user
    Alert.alert(
      'Meeting Accepted', 
      'You have accepted the meeting proposal. A QR code has been generated for the meeting.',
      [{ text: 'OK' }]
    );
  }, [messages, setMessages]); // Depends on messages and setMessages

  // Function to decline a meeting proposal
  const handleDeclineMeeting = useCallback((messageId: string) => {
    // Find the meeting proposal by its ID
    const proposalMessage = messages.find(msg => msg.id === messageId);
    
    if (!proposalMessage) {
      console.error('Could not find meeting proposal message with ID:', messageId);
      Alert.alert('Error', 'Could not find the meeting proposal.');
      return;
    }
    
    // Mark the meeting proposal as declined
    setMessages(prevMessages => 
      prevMessages.map(msg => {
        if (msg.id === messageId) {
          return {
            ...msg,
            isDeclined: true
          };
        }
        return msg;
      })
    );
    
    // Show confirmation to the user
    Alert.alert(
      'Meeting Declined', 
      'You have declined the meeting proposal. The other user can still send new proposals.',
      [{ text: 'OK' }]
    );
    
    try {
      // Create a decline notification content
      const declineContent = JSON.stringify({
        type: 'meetingDeclined',
        originalMessageId: messageId,
        time: new Date().toISOString(),
        displayAsDeclined: true // Special flag for the UI
      });
      
      // Send a message to the API indicating the decline
      sendMessage(offerId, declineContent)
        .then(() => {
          console.log('Meeting decline notification sent to API');
        })
        .catch(error => {
          console.error('Failed to send meeting decline notification:', error);
        });
    } catch (error) {
      console.error('Error sending meeting decline notification:', error);
    }
  }, [messages, offerId, setMessages]); // Depends on messages, offerId, and setMessages

  // Return the handler functions so components can use them
  return {
    handleMakeOffer,
    handleAcceptMeeting,
    handleDeclineMeeting,
  };
};
