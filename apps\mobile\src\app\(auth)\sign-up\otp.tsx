import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  OTPForm,
  otpSchema,
  OtpSchema,
  sendOtp,
  useSignUpStore,
  verifyOtp,
} from '@/pages/auth';

import { Button, ButtonText, Center, Heading, Text, VStack } from '@/shared/ui';

export default function SignUpOtpPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [userId, setUserId] = useState<string | null>(null);
  const { credentials, reset, isSocialAccount } = useSignUpStore();

  // Check for userId in route params
  useEffect(() => {
    if (params.userId) {
      setUserId(params.userId as string);
    }
  }, [params]);

  const form = useForm<OtpSchema>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      code: '',
    },
  });

  // Send OTP on component mount
  useEffect(() => {
    sendOtpCode();
  }, []);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Send OTP code
  const sendOtpCode = async () => {
    try {
      if (!userId) {
        throw new Error('User ID not available');
      }

      setIsLoading(true);

      // Send email verification OTP
      await sendOtp({
        type: 'email-verification',
        userId,
      });

      // Reset countdown
      setCountdown(60);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP code
  const handleVerify = async (data: OtpSchema) => {
    try {
      setIsLoading(true);

      if (!userId) {
        throw new Error('User ID not available');
      }

      // Verify OTP
      await verifyOtp({
        userId,
        otp: data.code,
        type: 'email-verification',
      });

      // Reset sign-up store
      reset();

      // Redirect to sign-in or main page
      router.replace('/sign-in');
    } catch (error) {
      console.error(error);
      form.setError('code', {
        message: 'Invalid verification code. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center className="h-full p-6 -mt-6 bg-background-0">
      <VStack space="lg" className="mx-6 w-full">
        <VStack space="md" className="min-w-full">
          <Heading size="3xl" className="text-typography-900">
            Verify your email
          </Heading>

          <Text className="text-typography-600 mb-4">
            {isSocialAccount
              ? `An account with email ${credentials.email} already exists. Please verify your email to set a password for this account.`
              : `We've sent a verification code to ${credentials.email}. Please enter it below to verify your account.`}
          </Text>

          <OTPForm form={form} />

          <Button
            onPress={() => {
              if (countdown === 0) {
                sendOtpCode();
              }
            }}
            variant="link"
            isDisabled={countdown > 0}
            className="mt-2"
          >
            <ButtonText>
              Resend code {countdown > 0 && `(${countdown}s)`}
            </ButtonText>
          </Button>

          <Button
            className="bg-blue-600 mt-4"
            onPress={form.handleSubmit(handleVerify)}
            size="xl"
            isDisabled={isLoading}
          >
            <ButtonText className="text-typography-0">
              {isLoading ? 'Verifying...' : 'Verify'}
            </ButtonText>
          </Button>
        </VStack>
      </VStack>
    </Center>
  );
}
