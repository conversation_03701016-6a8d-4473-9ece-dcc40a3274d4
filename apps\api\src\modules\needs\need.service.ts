import type { UserSelectDto } from '@/modules/users/user.dto';

import type { NeedCreateDto, NeedSelectDto, NeedUpdateDto } from './need.dto';
import { needRepository } from './need.repository';

export const needService = {
  async createNeed(data: NeedCreateDto) {
    const newNeed = await needRepository.create(data);

    return newNeed;
  },

  async getAllNeeds() {
    const allNeeds = await needRepository.findAll();

    return allNeeds;
  },

  async getUserNeeds(userId: UserSelectDto['id']) {
    const userNeeds = await needRepository.findByUserId(userId);

    return userNeeds;
  },

  async getNeed(id: NeedSelectDto['id']) {
    const foundNeed = await needRepository.findById(id);

    return foundNeed;
  },

  async updateNeed(id: NeedSelectDto['id'], data: NeedUpdateDto) {
    const updatedNeed = await needRepository.updateById(id, data);

    return updatedNeed;
  },

  async deleteNeed(id: NeedSelectDto['id']) {
    const deletedNeed = await needRepository.deleteById(id);

    return deletedNeed;
  },
};
