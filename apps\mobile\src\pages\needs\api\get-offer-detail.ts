import { nativeFetch } from '@/shared/lib';
import { User } from '@/shared/model';

export type OfferDetail = {
  id: string;
  needId: string;
  userId: string;
  price: number;
  status: string;
  createdAt: string;
  updatedAt?: string;
  need: {
    id: string;
    title: string;
    description: string;
    userId: string;
    categoryId: string;
    location: {
      x: number;
      y: number;
    };
    createdAt: string;
    user: User;
  };
  latestMessage: {
    id: string;
    offerId: string;
    senderId: string;
    content: string;
    status: string;
    createdAt: string;
    updatedAt?: string;
  };
};

export async function getOfferDetail(offerId: string) {
  const response = await nativeFetch(`/api/offers/${offerId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch offer detail');
  }

  const data = await response.json();
  return data.offer as OfferDetail;
}
