import { fetchJson } from '@/shared/lib';

export type Category = {
  id: string;
  name: string;
};

export async function getCategories(): Promise<Category[]> {
  const [error, data] = await fetchJson<{ categories: Category[] }>(
    '/api/categories',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (error) {
    throw error;
  }
  return data.categories;
}
