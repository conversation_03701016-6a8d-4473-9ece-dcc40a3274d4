import { nativeFetch, HttpError } from '@/shared/lib';

export type Category = {
  id: string;
  name: string;
};

export async function getCategories() {
  const [error, response] = await nativeFetch<{ categories: Category[] }>(
    '/api/categories',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (error) {
    return [error, undefined];
  }

  const data = await response.json();
  return [undefined, data.categories];
}
