import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';

import { encryptSecureEnvDto, decryptSecureEnvDto } from './env.dto';
import { envService } from './env.service';

export const envRoutes = new Hono()
  .post('/encrypt', zValidator('json', encryptSecureEnvDto), async (c) => {
    const body = c.req.valid('json');

    const encryptedValue = envService.encryptSecureEnv(body.input);

    return c.json({ output: encryptedValue });
  })

  .post('/decrypt', zValidator('json', decryptSecureEnvDto), async (c) => {
    const body = c.req.valid('json');

    const decryptedValue = envService.decryptSecureEnv(body.input);

    return c.json({ output: decryptedValue });
  });
