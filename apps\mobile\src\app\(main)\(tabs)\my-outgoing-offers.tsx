import React, { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
} from 'react-native';
import { useOfferStore } from '@/shared/model';
import { updateOffer } from '@/shared/api';
import type { Offer } from '@/shared/api';
import {
  HStack,
  VStack,
  Heading,
  Divider,
  Icon,
  ChevronRightIcon,
  ArrowLeftIcon,
} from '@/shared/ui';

export default function MyOutgoingOffers() {
  const router = useRouter();

  const {
    fetchOutgoingOffers,
    outgoingOffers,
    isLoading: isLoadingOffers,
    updateOffer: updateOfferInStore,
  } = useOfferStore();

  const [error, setError] = useState<string | null>(null);
  const [expandedOffers, setExpandedOffers] = useState<Record<string, boolean>>(
    {}
  );
  const [processingOffers, setProcessingOffers] = useState<Set<string>>(
    new Set()
  );

  // Load outgoing offers
  useEffect(() => {
    fetchOutgoingOffers();
  }, [fetchOutgoingOffers]);

  // Group offers by need
  const offersByNeed: Record<string, Offer[]> = outgoingOffers.reduce(
    (acc: Record<string, Offer[]>, offer: Offer) => {
      if (!acc[offer.needId]) {
        acc[offer.needId] = [];
      }
      acc[offer.needId].push(offer);
      return acc;
    },
    {} as Record<string, Offer[]>
  );

  // Toggle expanded state for a need
  const toggleNeedExpanded = (needId: string) => {
    setExpandedOffers((prev) => ({
      ...prev,
      [needId]: !prev[needId],
    }));
  };

  // Navigate to offer detail
  const handleOfferPress = (offerId: string) => {
    router.push(`/offers/${offerId}` as any);
  };

  // Cancel an offer
  const handleCancelOffer = async (offerId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      await updateOffer(offerId, { status: 'rejected' });
      await updateOfferInStore(offerId, { status: 'rejected' });
      Alert.alert('Success', 'Offer cancelled successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to cancel offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // Go back
  const handleBack = () => {
    router.back();
  };

  // Loading state
  if (isLoadingOffers) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#0000ff" />
        <Text className="mt-3 text-base">Loading...</Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-5 bg-white">
        <Text className="text-red-500 text-base text-center">
          Error: {error}
        </Text>
      </View>
    );
  }

  // Empty state
  if (outgoingOffers.length === 0) {
    return (
      <View className="flex-1 bg-white">
        <View className="flex-row items-center p-4 border-b border-gray-200">
          <TouchableOpacity
            onPress={handleBack}
            className="mr-4 flex-row items-center"
          >
            <Icon as={ArrowLeftIcon} size="sm" />
            <Text className="text-sm ml-1">Back</Text>
          </TouchableOpacity>
          <Text className="text-lg font-semibold">My Outgoing Offers</Text>
          <TouchableOpacity
            onPress={() => router.push('/my-offers' as any)}
            className="ml-auto bg-blue-500 py-1 px-3 rounded-lg"
          >
            <Text className="text-white text-sm">My Incoming Offers</Text>
          </TouchableOpacity>
        </View>
        <View className="flex-1 justify-center items-center p-5">
          <Text className="text-gray-500 text-base text-center">
            You haven't made any offers yet.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={handleBack}
          className="mr-4 flex-row items-center"
        >
          <Icon as={ArrowLeftIcon} size="sm" />
          <Text className="text-sm ml-1">Back</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold">My Outgoing Offers</Text>
        <TouchableOpacity
          onPress={() => router.push('/my-offers' as any)}
          className="ml-auto bg-blue-500 py-1 px-3 rounded-lg"
        >
          <Text className="text-white text-sm">My Incoming Offers</Text>
        </TouchableOpacity>
      </View>

      {/* List of needs and offers */}
      <ScrollView className="flex-1">
        {Object.entries(offersByNeed).map(([needId, offers]) => {
          const need = offers[0]?.need;
          const isExpanded = expandedOffers[needId];

          if (!need) return null;

          return (
            <View key={needId} className="mb-3">
              {/* Need title with expansion button */}
              <TouchableOpacity
                onPress={() => toggleNeedExpanded(needId)}
                className="mx-3 my-2 bg-white rounded-xl shadow-sm overflow-hidden"
              >
                <View className="p-4 flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <View className="mr-3 w-8 h-8 justify-center items-center bg-blue-100 rounded-lg">
                      <Text className="text-blue-700">📦</Text>
                    </View>
                    <View>
                      <Text className="font-semibold">{need.title}</Text>
                      <Text className="text-xs text-gray-500">Your offers</Text>
                    </View>
                  </View>
                  <View className="flex-row items-center">
                    <View className="bg-blue-100 px-2 py-1 rounded-full mr-2">
                      <Text className="text-blue-800 text-xs">
                        {offers.length} offers
                      </Text>
                    </View>
                    {!isExpanded && <Icon as={ChevronRightIcon} size="sm" />}
                  </View>
                </View>
              </TouchableOpacity>

              {/* Offers for this need */}
              {isExpanded &&
                offers.map((offer) => {
                  const isProcessing = processingOffers.has(offer.id);
                  const isAccepted = offer.status === 'accepted';
                  const isRejected = offer.status === 'rejected';

                  return (
                    <View
                      key={offer.id}
                      className={`mx-3 my-2 rounded-xl shadow-sm overflow-hidden ${
                        isAccepted
                          ? 'bg-green-50'
                          : isRejected
                          ? 'bg-red-50'
                          : 'bg-blue-50'
                      }`}
                    >
                      <View className="p-4">
                        {/* Offer details */}
                        <View className="flex-row items-center mb-2">
                          <View>
                            <Text className="font-semibold">{need.title}</Text>
                            <Text className="text-xs text-gray-500">
                              {isAccepted
                                ? 'Accepted'
                                : isRejected
                                ? 'Rejected/Cancelled'
                                : 'Pending'}
                            </Text>
                          </View>
                          <TouchableOpacity
                            className="ml-auto"
                            onPress={() => handleOfferPress(offer.id)}
                            disabled={isProcessing}
                          >
                            <Text className="text-xl">💬</Text>
                          </TouchableOpacity>
                        </View>

                        <Text className="text-sm mb-3">
                          Your offer: {offer.price}€
                        </Text>

                        {/* Actions */}
                        <View className="flex-row items-center justify-end">
                          {isProcessing ? (
                            <ActivityIndicator size="small" color="#0000ff" />
                          ) : isAccepted ? (
                            <Text className="text-green-600 font-semibold">
                              Accepted by recipient
                            </Text>
                          ) : isRejected ? (
                            <Text className="text-red-600 font-semibold">
                              Cancelled/Rejected
                            </Text>
                          ) : (
                            <TouchableOpacity
                              className="bg-red-500 py-2 px-4 rounded-lg"
                              onPress={() => handleCancelOffer(offer.id)}
                            >
                              <Text className="text-white">Cancel Offer</Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    </View>
                  );
                })}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}
