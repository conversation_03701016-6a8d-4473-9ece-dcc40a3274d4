import React from 'react';
import { MeetingQRCode } from '../meeting-qr-code';
import { AcceptedMeetingProps } from '../types/message-types';

function AcceptedMeeting(props: AcceptedMeetingProps) {
  const { 
    timestamp, 
    meetingTime, 
    meetingId,
    userRole 
  } = props;
  return (
    <MeetingQRCode
      meetingId={meetingId}
      meetingTime={meetingTime}
      timestamp={timestamp}
      userRole={userRole}
    />
  );
}

export default AcceptedMeeting;
