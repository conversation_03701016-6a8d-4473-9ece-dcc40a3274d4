import type { Context, Next } from 'hono';
import { describe, expect, it, vi, beforeEach } from 'vitest';

import { app } from '@/app';

import { sessionService, sessionTokenCookieName } from '@/modules/auth';

import type { AuthVariables, ContextSession } from '@/shared/model';

const testUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  phoneNumber: null,
  imageRef: null,
  isEmailVerified: true,
  createdAt: new Date(),
  updatedAt: null,
};

const testSession = {
  id: 'test-session-id',
  userId: 'test-user-id',
  ipAddress: '127.0.0.1',
  userAgent: 'Mozilla/5.0',
  expiresAt: new Date(),
  createdAt: new Date(),
  updatedAt: null,
  user: testUser,
} satisfies ContextSession;

// Mock the session middleware to provide a session
vi.mock('@/shared/middlewares', async () => {
  const actual = await vi.importActual('@/shared/middlewares');
  return {
    ...actual,
    sessionMiddleware: vi.fn().mockImplementation(
      (
        c: Context<{
          Variables: AuthVariables;
        }>,
        next: Next
      ) => {
        // Mock session data
        c.set('session', testSession);
        return next();
      }
    ),
  };
});

// Mock the session service
vi.mock('@/modules/auth/services/session.service', () => ({
  sessionService: {
    deleteSessionById: vi.fn().mockResolvedValue(true),
  },
}));

describe('Auth Routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /auth/session', () => {
    it('should return the current session', async () => {
      const res = await app.request('/api/auth/session');

      expect(res.status).toBe(200);
      const data = await res.json();

      expect(data).toEqual({
        session: {
          ...testSession,
          createdAt: expect.any(String),
          expiresAt: expect.any(String),
          updatedAt: expect.toSatisfy(
            (value) => typeof value === 'string' || value === null
          ),
          user: {
            ...testUser,
            createdAt: expect.any(String),
            updatedAt: expect.toSatisfy(
              (value) => typeof value === 'string' || value === null
            ),
          },
        },
      });
    });
  });

  describe('POST /auth/sign-out', () => {
    it('should delete the session and clear the cookie', async () => {
      const res = await app.request('/api/auth/sign-out', {
        method: 'POST',
      });

      expect(res.status).toBe(200);
      const data = await res.json();

      expect(data).toEqual({
        message: 'Logout successful',
      });

      // Verify the session was deleted
      expect(sessionService.deleteSessionById).toHaveBeenCalledWith(
        'test-session-id'
      );

      // Check that the cookie was deleted
      expect(res.headers.get('Set-Cookie')).toContain(
        `${sessionTokenCookieName}=;`
      );
    });
  });
});
