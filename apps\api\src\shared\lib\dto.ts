import type { z } from 'zod';

export function applyDtoSchema<T extends z.ZodObject<any>>(
  data: z.input<T> & Record<string, unknown>, // Combine z.input<T> and Record<string, unknown>
  schema: T
): z.infer<T> {
  const schemaKeys = Object.keys(schema.shape);

  // Filter out keys not present in the schema
  const filteredData = Object.fromEntries(
    Object.entries(data).filter(([key]) => schemaKeys.includes(key))
  );

  return filteredData as z.infer<T>;
}
