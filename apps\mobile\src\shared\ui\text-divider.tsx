import { View } from 'react-native';

import { Divider, HStack, Text } from '@/shared/ui';

interface ITextDividerProps {
  children: string;
}

export function TextDivider({ children }: ITextDividerProps) {
  return (
    // <View>
    <HStack space="md" className="justify-center items-center">
      <Divider className="flex-1" />
      <Text>{children}</Text>
      <Divider className="flex-1" />
    </HStack>
    // </View>
  );
}
