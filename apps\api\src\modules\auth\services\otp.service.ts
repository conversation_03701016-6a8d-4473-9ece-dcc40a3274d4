import { randomInt } from 'node:crypto';

import { userService } from '@/modules/users';

import { getDateFromNow, resend } from '@/shared/lib';

// In-memory OTP storage (in a production app, this would be in a database)
interface OtpRecord {
  otp: string;
  userId: string;
  type: string;
  expiresAt: Date;
  createdAt: Date;
  lastResent?: Date;
}

const otpStore = new Map<string, OtpRecord>();

// OTP types
export type OtpType =
  | 'email-verification'
  | 'sign-in'
  | 'forget-password'
  | 'update-password';

// OTP configuration
const OTP_LENGTH = 6;
const OTP_EXPIRY = '5m'; // 5 minutes
const OTP_RESEND_COOLDOWN = '60s'; // 60 seconds

export const otpService = {
  /**
   * Generate a new OTP for a user
   */
  async generateOtp(userId: string, type: OtpType): Promise<string> {
    // Generate a random 6-digit OTP
    const otp = randomInt(100000, 999999).toString();

    // Store the OTP with expiration
    const expiresAt = getDateFromNow(OTP_EXPIRY);
    const key = `${userId}:${type}`;

    // Check if there's an existing OTP with cooldown
    const existingOtp = otpStore.get(key);
    if (existingOtp && existingOtp.lastResent) {
      const cooldownEnd = new Date(existingOtp.lastResent.getTime() + 60000); // 60 seconds
      if (new Date() < cooldownEnd) {
        const remainingSeconds = Math.ceil(
          (cooldownEnd.getTime() - Date.now()) / 1000
        );
        throw new Error(
          `Please wait ${remainingSeconds} seconds before requesting a new OTP`
        );
      }
    }

    otpStore.set(key, {
      otp,
      userId,
      type,
      expiresAt,
      createdAt: new Date(),
      lastResent: new Date(),
    });

    return otp;
  },

  /**
   * Send an OTP via email
   */
  async sendOtpEmail(userId: string, type: OtpType): Promise<void> {
    // Get user details
    const user = await userService.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Generate OTP
    const otp = await this.generateOtp(userId, type);

    // Prepare email content based on OTP type
    let subject = '';
    let content = '';

    switch (type) {
      case 'email-verification':
        subject = 'Verify your email address';
        content = `
          <h1>Email Verification</h1>
          <p>Hello ${user.firstName},</p>
          <p>Your verification code is: <strong>${otp}</strong></p>
          <p>This code will expire in 5 minutes.</p>
        `;
        break;
      case 'sign-in':
        subject = 'Sign in to your account';
        content = `
          <h1>Sign In Code</h1>
          <p>Hello ${user.firstName},</p>
          <p>Your sign in code is: <strong>${otp}</strong></p>
          <p>This code will expire in 5 minutes.</p>
        `;
        break;
      case 'forget-password':
        subject = 'Reset your password';
        content = `
          <h1>Password Reset</h1>
          <p>Hello ${user.firstName},</p>
          <p>Your password reset code is: <strong>${otp}</strong></p>
          <p>This code will expire in 5 minutes.</p>
        `;
        break;
      case 'update-password':
        subject = 'Update your password';
        content = `
          <h1>Password Update</h1>
          <p>Hello ${user.firstName},</p>
          <p>Your password update code is: <strong>${otp}</strong></p>
          <p>This code will expire in 5 minutes.</p>
        `;
        break;
      default:
        throw new Error('Invalid OTP type');
    }

    // Send email
    await resend.emails.send({
      from: 'Acme <<EMAIL>>',
      to: '<EMAIL>',
      subject,
      html: content,
    });
  },

  /**
   * Verify an OTP
   */
  async verifyOtp(
    userId: string,
    otp: string,
    type: OtpType
  ): Promise<boolean> {
    const key = `${userId}:${type}`;
    const otpRecord = otpStore.get(key);

    if (!otpRecord) {
      throw new Error('OTP not found');
    }

    // Check if OTP is expired
    if (new Date() > otpRecord.expiresAt) {
      otpStore.delete(key);
      throw new Error('OTP has expired');
    }

    // Check if OTP matches
    if (otpRecord.otp !== otp) {
      throw new Error('Invalid OTP');
    }

    // OTP is valid, delete it to prevent reuse
    otpStore.delete(key);

    return true;
  },

  /**
   * Mark email as verified for a user
   */
  async markEmailAsVerified(userId: string): Promise<void> {
    await userService.updateById(userId, { isEmailVerified: true });
  },
};
