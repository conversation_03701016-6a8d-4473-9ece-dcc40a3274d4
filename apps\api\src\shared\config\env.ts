import * as dotenv from 'dotenv';
import type { LevelWithSilentOrString } from 'pino';
import { z } from 'zod';

dotenv.config();

const EnvSchema = z.object({
  // Node environment
  NODE_ENV: z.string().default('development'),
  // Server
  PORT: z.coerce.number().default(3000),
  LOG_LEVEL: z
    .enum([
      'fatal',
      'error',
      'warn',
      'info',
      'debug',
      'trace',
      'silent',
    ] as const satisfies readonly LevelWithSilentOrString[])
    .default('debug'),
  // Database
  DATABASE_URL: z.string().url(),
  // Clients
  WEB_CLIENT_URL: z.string().url(),
  MOBILE_CLIENT_SCHEME: z.string().min(1),
  // Emails
  RESEND_API_KEY: z.string().min(1),
  // Storage
  SUPABASE_URL: z.string().url(),
  SUPABASE_ANON_KEY: z.string().min(1),
  // Secure environment variables
  ENV_ENCRYPTION_KEY: z.string().min(1),
  // Payments
  STRIPE_SECRET_KEY: z.string().min(1),
});

const { data: env, error } = EnvSchema.safeParse(process.env);

if (error) {
  console.error('❌ Invalid env:');
  console.error(JSON.stringify(error.flatten().fieldErrors, null, 2));
  process.exit(1);
}

export default env!;
