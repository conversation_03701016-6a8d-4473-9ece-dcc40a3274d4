import React, { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
} from 'react-native';
import { useOfferStore } from '@/shared/model';
import { getUserNeeds, getCategories } from '@/pages/needs';
import { updateOffer } from '@/shared/api';
import type { NeedWithUser } from '@/pages/needs/api/get-user-needs';
import type { Offer as IncomingOffer } from '@/shared/api';
import type { Category } from '@/pages/needs/api/categories';
import {
  HStack,
  VStack,
  Heading,
  Divider,
  Icon,
  ChevronRightIcon,
  ArrowLeftIcon,
} from '@/shared/ui';

export default function MyOffers() {
  const router = useRouter();

  // This line can be used to produce reports to manage offers and requirements
  const {
    fetchIncomingOffers,
    incomingOffers,
    isLoading: isLoadingOffers,
    updateOffer: updateOfferInStore,
  } = useOfferStore();
  const [userNeeds, setUserNeeds] = useState<NeedWithUser[]>([]); // This line is the list of user requirements
  const [categories, setCategories] = useState<Category[]>([]); // List of categories
  const [isLoadingNeeds, setIsLoadingNeeds] = useState(false); // This line shows the loading status of the requirements
  const [isLoadingCategories, setIsLoadingCategories] = useState(false); // Loading status for categories
  const [error, setError] = useState<string | null>(null); // This line is used to manage errors
  const [expandedOffers, setExpandedOffers] = useState<Record<string, boolean>>(
    {}
  );
  const [seenOffers, setSeenOffers] = useState<Set<string>>(new Set()); // This line allows you to track offers that have already been consulted (via their ID)
  const [processingOffers, setProcessingOffers] = useState<Set<string>>(
    new Set()
  ); // Track offers being processed

  // This line is the effect of loading the user's requirements at startup
  useEffect(() => {
    const fetchNeeds = async () => {
      setIsLoadingNeeds(true);
      try {
        const needs = await getUserNeeds();
        setUserNeeds(needs);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch needs');
      } finally {
        setIsLoadingNeeds(false);
      }
    };

    fetchNeeds(); // This line is used to call the function to load the requirements
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const fetchedCategories = await getCategories();
        setCategories(fetchedCategories);
      } catch (err) {
        console.error('Failed to fetch categories:', err);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // This line is used to load incoming offers
  useEffect(() => {
    fetchIncomingOffers();
  }, [fetchIncomingOffers]);

  // This line groups offers by need
  const offersByNeed: Record<string, IncomingOffer[]> = incomingOffers.reduce(
    (acc: Record<string, IncomingOffer[]>, offer: IncomingOffer) => {
      if (!acc[offer.needId]) {
        acc[offer.needId] = [];
      }
      acc[offer.needId].push(offer); // This line adds the offer to the corresponding requirement
      return acc;
    },
    {} as Record<string, IncomingOffer[]>
  );

  // This line calls the function for alternating the display of offers for a requirement (expand/collapse)
  const toggleNeedExpanded = (needId: string) => {
    setExpandedOffers((prev) => ({
      ...prev,
      [needId]: !prev[needId], // This line is used to reverse the expansion status of the requirement
    }));
  };

  // This line allows you to mark an offer as viewed and navigate to the details page.
  const handleOfferPress = (offerId: string) => {
    setSeenOffers((prev) => new Set(prev).add(offerId));
    router.push(`/offers/${offerId}` as any);
  };

  // Handle accepting an offer
  const handleAcceptOffer = async (offerId: string, needId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      // Accept the selected offer
      await updateOffer(offerId, { status: 'accepted' });

      // Update the local store
      // First, find all offers for this need
      const needOffers = incomingOffers.filter(
        (offer) => offer.needId === needId
      );

      // Update the status of all offers for this need
      for (const offer of needOffers) {
        if (offer.id === offerId) {
          // Mark the selected offer as accepted
          await updateOfferInStore(offer.id, { status: 'accepted' });
        } else {
          // Mark all other offers for this need as rejected
          await updateOfferInStore(offer.id, { status: 'rejected' });
        }
      }

      Alert.alert('Success', 'Offer accepted successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to accept offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // Handle rejecting an offer
  const handleRejectOffer = async (offerId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      await updateOffer(offerId, { status: 'rejected' });
      await updateOfferInStore(offerId, { status: 'rejected' });
      Alert.alert('Success', 'Offer rejected successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to reject offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // This line allows the function to go backwards in navigation
  const handleBack = () => {
    router.back();
  };

  // This line displays a loading indicator while the data is loading
  if (isLoadingNeeds || isLoadingOffers || isLoadingCategories) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#0000ff" />
        <Text className="mt-3 text-base">Loading...</Text>
      </View>
    );
  }

  // This line displays an error if there is a problem loading the data
  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-5 bg-white">
        <Text className="text-red-500 text-base text-center">
          Error: {error}
        </Text>
      </View>
    );
  }

  // This line is used to obtain an avatar URL (demonstration)
  const getAvatarUrl = () => 'https://i.pravatar.cc/100';

  return (
    <View className="flex-1 bg-white">
      {/* This line is the page header */}
      <View className="flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={handleBack}
          className="mr-4 flex-row items-center"
        >
          <Icon as={ArrowLeftIcon} size="sm" />
          <Text className="text-sm ml-1">Back</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold">My Incoming Offers</Text>
        <TouchableOpacity
          onPress={() => router.push('/my-outgoing-offers' as any)}
          className="ml-auto bg-blue-500 py-1 px-3 rounded-lg"
        >
          <Text className="text-white text-sm">My Outgoing Offers</Text>
        </TouchableOpacity>
      </View>

      {/* This line is the list of needs and offers */}
      <ScrollView className="flex-1">
        {userNeeds.map((need) => {
          const needOffers = offersByNeed[need.id] || []; // This line retrieves bids for this requirement
          const isExpanded = expandedOffers[need.id]; // This line checks whether the requirement is expanded

          return (
            <View key={need.id} className="mb-3">
              {/* This line is the title of the requirement with a button for expansion */}
              <TouchableOpacity
                onPress={() => toggleNeedExpanded(need.id)}
                className="mx-3 my-2 bg-white rounded-xl shadow-sm overflow-hidden"
              >
                <View className="p-4 flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <View className="mr-3 w-8 h-8 justify-center items-center bg-red-100 rounded-lg">
                      <Text className="text-red-700">🍕</Text>
                    </View>
                    <View>
                      <Text className="font-semibold">{need.title}</Text>
                      <Text className="text-xs text-gray-500">
                        {categories.find((cat) => cat.id === need.categoryId)
                          ?.name || 'Unknown Category'}
                      </Text>
                    </View>
                  </View>
                  <View className="flex-row items-center">
                    <View className="bg-blue-100 px-2 py-1 rounded-full mr-2">
                      <Text className="text-blue-800 text-xs">
                        {needOffers.length} offres
                      </Text>
                    </View>
                    {!isExpanded && <Icon as={ChevronRightIcon} size="sm" />}
                  </View>
                </View>
              </TouchableOpacity>

              {/* This line displays the offers for the requirement if the requirement is expanded.*/}
              {isExpanded &&
                needOffers.map((offer) => {
                  const isProcessing = processingOffers.has(offer.id);
                  const isAccepted = offer.status === 'accepted';
                  const isRejected = offer.status === 'rejected';

                  return (
                    <View
                      key={offer.id}
                      className={`mx-3 my-2 rounded-xl shadow-sm overflow-hidden ${
                        isAccepted
                          ? 'bg-green-50'
                          : isRejected
                          ? 'bg-red-50'
                          : seenOffers.has(offer.id)
                          ? 'bg-gray-200'
                          : 'bg-blue-50'
                      }`}
                    >
                      <View className="p-4">
                        {/* This line is the news of the offer */}
                        <View className="flex-row items-center mb-2">
                          <Image
                            source={{ uri: getAvatarUrl() }}
                            className="w-8 h-8 rounded-full mr-3"
                          />
                          <View>
                            <Text className="font-semibold">
                              {offer.user
                                ? `${offer.user.firstName} ${offer.user.lastName}`
                                : 'Unknown User'}
                            </Text>
                            <Text className="text-xs text-gray-500">
                              À 350m • il y a 5 min
                            </Text>
                          </View>
                          <TouchableOpacity
                            className="ml-auto"
                            onPress={() => handleOfferPress(offer.id)}
                            disabled={isProcessing}
                          >
                            <Text className="text-xl">💬</Text>
                          </TouchableOpacity>
                        </View>

                        <Text className="font-semibold mb-1">{need.title}</Text>
                        <Text className="text-sm mb-3">{need.description}</Text>

                        {/* This line is the button for accepting or refusing the offer */}
                        <View className="flex-row items-center justify-between">
                          <Text className="font-bold text-lg">
                            {offer.price}€
                          </Text>

                          {isProcessing ? (
                            <ActivityIndicator size="small" color="#0000ff" />
                          ) : isAccepted ? (
                            <Text className="text-green-600 font-semibold">
                              Accepted
                            </Text>
                          ) : isRejected ? (
                            <Text className="text-red-600 font-semibold">
                              Rejected
                            </Text>
                          ) : (
                            <View className="flex-row">
                              <TouchableOpacity
                                className="bg-gray-200 py-2 px-4 rounded-lg mr-2"
                                onPress={() => handleRejectOffer(offer.id)}
                              >
                                <Text className="text-gray-800">Refuser</Text>
                              </TouchableOpacity>
                              <TouchableOpacity
                                className="bg-blue-500 py-2 px-4 rounded-lg"
                                onPress={() =>
                                  handleAcceptOffer(offer.id, need.id)
                                }
                              >
                                <Text className="text-white">Accepter</Text>
                              </TouchableOpacity>
                            </View>
                          )}
                        </View>
                      </View>
                    </View>
                  );
                })}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}
