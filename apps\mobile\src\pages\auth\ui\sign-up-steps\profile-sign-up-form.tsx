import { View } from 'react-native';

import { Controller, UseFormReturn } from 'react-hook-form';

import { type ProfileSchema } from '@/pages/auth';

import {
  AlertCircleIcon,
  Button,
  ButtonText,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  HStack,
  Image,
  Input,
  InputField,
  Text,
  VStack,
} from '@/shared/ui';

interface Props {
  form: UseFormReturn<ProfileSchema>;
  onPickImage: () => void;
  onTakePhoto: () => void;
}

export function ProfileSignUpForm({ form, onPickImage, onTakePhoto }: Props) {
  const imageUri = form.watch('imageUri');

  return (
    <VStack space="md">
      <VStack space="md" className="items-center">
        {imageUri ? (
          <Image
            source={{ uri: imageUri }}
            className="w-24 h-24 rounded-full mb-2"
            alt="Profile image"
          />
        ) : (
          <View className="w-24 h-24 rounded-full bg-gray-200 items-center justify-center mb-2">
            <Text className="text-gray-400 text-xl">No Image</Text>
          </View>
        )}

        <HStack space="md">
          <Button onPress={onTakePhoto} variant="outline" size="sm">
            <ButtonText>Take Photo</ButtonText>
          </Button>
          <Button onPress={onPickImage} variant="outline" size="sm">
            <ButtonText>Choose Image</ButtonText>
          </Button>
        </HStack>
      </VStack>

      <Controller
        control={form.control}
        name="firstName"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText>First name</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type="text"
                {...field}
                onChangeText={field.onChange}
              />
            </Input>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />

      <Controller
        control={form.control}
        name="lastName"
        render={({ field, fieldState }) => (
          <FormControl isInvalid={fieldState.error !== undefined} size="md">
            <FormControlLabel>
              <FormControlLabelText>Last name</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type="text"
                {...field}
                onChangeText={field.onChange}
              />
            </Input>
            <FormControlError>
              <FormControlErrorIcon as={AlertCircleIcon} />
              <FormControlErrorText>
                {fieldState.error?.message}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>
        )}
      />
    </VStack>
  );
}
