import * as crypto from 'node:crypto';

// Hashing
export function sha256Hash(
  input: string,
  encoding?: crypto.BinaryToTextEncoding
) {
  if (!encoding) return crypto.createHash('sha256').update(input).digest();

  return crypto.createHash('sha256').update(input).digest(encoding);
}

export function sha256Verify(
  input: string,
  expectedHash: string,
  encoding?: crypto.BinaryToTextEncoding
) {
  try {
    // Convert expected hash to Buffer
    const expectedBuffer = encoding
      ? Buffer.from(expectedHash, encoding)
      : Buffer.from(expectedHash);

    // Calculate actual hash as Buffer
    let actualBuffer: Buffer;
    if (encoding) {
      // When encoding is provided, sha256Hash returns a string
      const actualHashString = sha256Hash(input, encoding) as string;
      actualBuffer = Buffer.from(actualHashString, encoding);
    } else {
      // When no encoding is provided, sha256Hash returns a Buffer
      actualBuffer = sha256Hash(input) as Buffer;
    }

    return (
      expectedBuffer.length === actualBuffer.length &&
      crypto.timingSafeEqual(expectedBuffer, actualBuffer)
    );
  } catch {
    return false; // If the hash is invalid, return false
  }
}

// Encryption
export function encrypt(data: string, key: string) {
  const iv = crypto.randomBytes(16);
  const keyBuffer = sha256Hash(key);

  const cipher = crypto.createCipheriv('aes-256-cbc', keyBuffer, iv);

  const encrypted = cipher.update(data, 'utf8', 'hex') + cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

export function decrypt(data: string, key: string) {
  const [iv, encrypted] = data.split(':');
  const keyBuffer = sha256Hash(key);

  const decipher = crypto.createDecipheriv(
    'aes-256-cbc',
    keyBuffer,
    Buffer.from(iv, 'hex')
  );

  const decrypted =
    decipher.update(encrypted, 'hex', 'utf8') + decipher.final();
  return decrypted;
}

// Tokens
export function generateToken() {
  return crypto.randomBytes(32).toString('hex');
}
