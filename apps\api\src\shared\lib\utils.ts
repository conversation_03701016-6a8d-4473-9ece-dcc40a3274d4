import type { Duration, Unit } from '@/shared/model';

// Turn a string like "1d" into milliseconds
export function parseDuration(duration: Duration): number {
  const match = duration.match(/^(\d+)(d|h|m|s|ms)$/)!;

  const value = parseInt(match[1], 10);
  const unit = match[2] as Unit;

  switch (unit) {
    case 'd':
      return value * 1000 * 60 * 60 * 24;
    case 'h':
      return value * 1000 * 60 * 60;
    case 'm':
      return value * 1000 * 60;
    case 's':
      return value * 1000;
    case 'ms':
      return value;
  }
}

export function getDateFromNow(duration: Duration) {
  return new Date(Date.now() + parseDuration(duration));
}
