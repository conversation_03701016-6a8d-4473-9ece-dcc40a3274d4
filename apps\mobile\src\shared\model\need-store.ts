import { create } from 'zustand';
import { createNeed, getAllNeeds, getNeedDetail } from '@/pages/needs';
import type { Need } from '@/pages/needs';
import { User } from '@/shared/model';

// Define NeedDetail type to include user information
type NeedDetail = Need & { user: User };

interface NeedState {
  needs: Need[];
  selectedNeed: NeedDetail | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchNeeds: () => Promise<void>;
  fetchNeedDetail: (needId: string) => Promise<void>;
  addNeed: (need: Parameters<typeof createNeed>[0]) => Promise<Need>;
  updateNeed: (needId: string, need: Partial<Need>) => Promise<void>;
  deleteNeed: (needId: string) => Promise<void>;
  setSelectedNeed: (need: NeedDetail | null) => void;
  reset: () => void;
}

export const useNeedStore = create<NeedState>((set, get) => ({
  needs: [],
  selectedNeed: null,
  isLoading: false,
  error: null,

  fetchNeeds: async () => {
    set({ isLoading: true, error: null });
    try {
      const needs = await getAllNeeds();
      set({ needs, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch needs',
        isLoading: false,
      });
    }
  },

  fetchNeedDetail: async (needId: string) => {
    set({ isLoading: true, error: null });
    try {
      const need = await getNeedDetail(needId);
      set({ selectedNeed: need, isLoading: false });
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch need detail',
        isLoading: false,
      });
    }
  },

  addNeed: async (needData) => {
    set({ isLoading: true, error: null });
    try {
      const newNeed = await createNeed(needData);
      set((state) => ({
        needs: [...state.needs, newNeed],
        isLoading: false,
      }));
      return newNeed;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create need',
        isLoading: false,
      });
      throw error;
    }
  },

  updateNeed: async (needId, needData) => {
    set({ isLoading: true, error: null });
    try {
      // Implement API call when available
      // For now, update locally
      set((state) => ({
        needs: state.needs.map((need) =>
          need.id === needId ? { ...need, ...needData } : need
        ),
        selectedNeed:
          state.selectedNeed?.id === needId
            ? ({ ...state.selectedNeed, ...needData } as NeedDetail)
            : state.selectedNeed,
        isLoading: false,
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update need',
        isLoading: false,
      });
    }
  },

  deleteNeed: async (needId) => {
    set({ isLoading: true, error: null });
    try {
      // Implement API call when available
      // For now, delete locally
      set((state) => ({
        needs: state.needs.filter((need) => need.id !== needId),
        selectedNeed:
          state.selectedNeed?.id === needId ? null : state.selectedNeed,
        isLoading: false,
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete need',
        isLoading: false,
      });
    }
  },

  setSelectedNeed: (need) => {
    set({ selectedNeed: need as NeedDetail });
  },

  reset: () => {
    set({ needs: [], selectedNeed: null, isLoading: false, error: null });
  },
}));
