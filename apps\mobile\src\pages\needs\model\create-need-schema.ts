import { z } from 'zod';

const titleMinLength = 2;
const titleMaxLength = 64;

export const createNeedSchema = z.object({
  title: z
    .string()
    .min(titleMinLength, {
      message: `Title mush be at least ${titleMinLength} characters long`,
    })
    .max(titleMaxLength, {
      message: `Title mush be at most ${titleMaxLength} characters long`,
    }),
  description: z.string().min(1, { message: 'Description must not be empty' }),
  category: z.string(),
});
