import { eq } from 'drizzle-orm';

import { db, schema } from '@needit/db';

import type {
  CredentialsAccountCreateDto,
  CredentialsAccountUpdateDto,
} from '@/modules/auth';
import type { UserSelectDto } from '@/modules/users/user.dto';

export const credentialsAccountRepository = {
  async create(data: CredentialsAccountCreateDto) {
    return await db
      .insert(schema.credentialsAccounts)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findByUserId(userId: UserSelectDto['id']) {
    return await db
      .select()
      .from(schema.credentialsAccounts)
      .where(eq(schema.credentialsAccounts.userId, userId))
      .then((res) => res[0]);
  },

  async findByUserEmail(email: UserSelectDto['email']) {
    return await db
      .select()
      .from(schema.credentialsAccounts)
      .leftJoin(
        schema.users,
        eq(schema.credentialsAccounts.userId, schema.users.id)
      )
      .where(eq(schema.users.email, email))
      .then((res) => res[0]);
  },

  async updateById(
    userId: UserSelectDto['id'],
    data: CredentialsAccountUpdateDto
  ) {
    return await db
      .update(schema.credentialsAccounts)
      .set(data)
      .where(eq(schema.credentialsAccounts.userId, userId))
      .returning()
      .then((res) => res[0]);
  },
};
