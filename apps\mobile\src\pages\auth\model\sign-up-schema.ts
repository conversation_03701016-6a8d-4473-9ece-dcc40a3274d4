import { z } from 'zod';

export const credentialsSchema = z
  .object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export const profileSchema = z.object({
  imageUri: z.string().optional(),
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
});

export const noOfDigits = 6;
export const otpSchema = z.object({
  code: z.string().length(noOfDigits, `Code must be ${noOfDigits} digits`),
});

export type CredentialsSchema = z.infer<typeof credentialsSchema>;
export type ProfileSchema = z.infer<typeof profileSchema>;
export type OtpSchema = z.infer<typeof otpSchema>;
