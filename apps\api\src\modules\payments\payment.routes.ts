import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

import type { AuthVariables } from '@/shared/model';

import { paymentService } from './payment.service';

export const paymentRoutes = new Hono<{
  Variables: AuthVariables;
}>();

paymentRoutes
  .post(
    '/connect',
    zValidator('json', z.object({ email: z.string().email() })),
    async (c) => {
      const { email } = c.req.valid('json');
      const result = await paymentService.createConnectAccount(email);
      return c.json(result);
    }
  )
  .post(
    '/subscribe',
    zValidator('json', z.object({ priceId: z.string() })),
    async (c) => {
      const { priceId } = c.req.valid('json');
      const result = await paymentService.createCheckoutSession(priceId);
      return c.json(result);
    }
  )
  .post(
    '/request/:id/pay',
    zValidator(
      'json',
      z.object({
        amount: z.number().int().positive(),
        connectedAccountId: z.string(),
      })
    ),
    async (c) => {
      const { amount, connectedAccountId } = c.req.valid('json');
      const result = await paymentService.createPaymentIntent(
        amount,
        connectedAccountId
      );
      return c.json(result);
    }
  );
