import { createClient } from '@supabase/supabase-js';

import { env } from '@/shared/config';

// Initialize Supabase client
const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_ANON_KEY);

export const mediaRepository = {
  async uploadMedia(
    userId: string,
    file: Buffer,
    fileName: string,
    contentType: string
  ) {
    // Generate a unique file path, prepending 'temp/' if userId is empty
    const filePath = userId
      ? `${userId}/${Date.now()}_${fileName}`
      : `temp/${Date.now()}_${fileName}`;

    // Upload file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('user-uploads')
      .upload(filePath, file, {
        contentType,
        upsert: false,
      });

    if (error) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from('user-uploads').getPublicUrl(filePath);

    return {
      imageRef: filePath,
      url: publicUrl,
    };
  },

  async getMediaUrl(imageRef: string) {
    const {
      data: { publicUrl },
    } = supabase.storage.from('user-uploads').getPublicUrl(imageRef);

    return publicUrl;
  },

  async deleteMedia(imageRef: string) {
    const { error } = await supabase.storage
      .from('user-uploads')
      .remove([imageRef]);

    if (error) {
      throw new Error(`Failed to delete file: ${error.message}`);
    }

    return true;
  },

  async renameMedia(oldPath: string, newPath: string) {
    const { error } = await supabase.storage
      .from('user-uploads')
      .move(oldPath, newPath);

    if (error) {
      throw new Error(`Failed to rename file: ${error.message}`);
    }
  },
};
