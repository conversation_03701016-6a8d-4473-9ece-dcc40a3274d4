import { User } from '@/shared/model';

import { nativeFetch } from '@/shared/lib';

type Session = {
  id: string;
  userId: User['id'];
  userAgent: string;
  ipAdress: string;
  createdAt: string;
  updatedAt: string;
  user: User;
};

export async function getSession() {
  const response = await nativeFetch('/api/auth/session', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    return null;
  }

  const data = await response.json();
  return data.session as Session;
}

export async function signOut() {
  return await nativeFetch('/api/auth/sign-out', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });
}
