import { nativeFetch } from '@/shared/lib';

import env from './env';

export const secureEnv = {
  async get(name: keyof typeof env) {
    const value = env[name];

    const respone = await nativeFetch(`/api/env/decrypt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ input: value }),
      credentials: 'include',
    });

    if (!respone.ok) {
      console.error(`❌ Failed to get env variable ${secureEnv}`);
      process.exit(1);
    }

    const data = await respone.json();
    return data.output as string;
  },
};

export default secureEnv;
