import React, { useRef, useEffect } from 'react';
import {
  ScrollView,
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { MessageBubble } from '@/pages/messages';
import { Message } from '@/shared/api';

interface MessagesListProps {
  messages: Message[];
  isLoadingMessages: boolean;
  currentUserId: string | null;
  formatDate: (dateString: string) => string;
  onAcceptMeeting: (messageId: string) => void;
  onDeclineMeeting: (messageId: string) => void;
}

export const MessagesList: React.FC<MessagesListProps> = ({
  messages,
  isLoadingMessages,
  currentUserId,
  formatDate,
  onAcceptMeeting,
  onDeclineMeeting,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);

  // Scroll to bottom when messages are loaded or updated
  useEffect(() => {
    if (messages.length > 0 && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: false });
      }, 100);
    }
  }, [messages]);

  return (
    <ScrollView
      ref={scrollViewRef}
      className="flex-1 px-4"
      contentContainerStyle={{
        paddingTop: 20,
        paddingBottom: 20,
      }}
    >
      {isLoadingMessages ? (
        <ActivityIndicator size="small" color="#0000ff" />
      ) : messages.length > 0 ? (
        messages.map((msg) => {
          // Check if this is a meeting proposal message or a declined message
          let isMeetingProposal = false;
          let isDeclined = false;
          let meetingTime = '';

          // Check if the message has the isMeetingProposal property directly
          if ('isMeetingProposal' in msg && msg.isMeetingProposal) {
            isMeetingProposal = true;
            meetingTime = (msg as any).meetingTime || '';
            isDeclined = (msg as any).isDeclined || false;
          } else {
            // Try to parse the content as JSON to check if it's a meeting proposal or a declined message
            try {
              const content = JSON.parse(msg.content);
              if (content.type === 'meetingProposal') {
                isMeetingProposal = true;
                meetingTime = content.time;
              } else if (
                content.type === 'meetingDeclined' &&
                content.displayAsDeclined
              ) {
                // This is a declined message notification
                console.log('Found decline notification message:', msg.id);

                // Skip rendering this message as it's just a notification
                // We'll show the declined status on the original message
                return null;
              }
            } catch (e) {
              // Not a JSON message, so not a meeting proposal or declined message
            }
          }

          // Determine message type
          let messageType: 'regular' | 'proposal' | 'accepted' | 'declined' =
            'regular';

          if (isMeetingProposal) {
            if ((msg as any).isAccepted) {
              messageType = 'accepted';
            } else if ((msg as any).isDeclined) {
              messageType = 'declined';
            } else {
              messageType = 'proposal';
            }
          }

          // Prepare props based on message type
          let messageProps;

          switch (messageType) {
            case 'regular':
              messageProps = {
                id: msg.id,
                text: msg.content,
                sender: (msg.senderId === currentUserId ? 'user' : 'other') as
                  | 'user'
                  | 'other',
                timestamp: formatDate(msg.createdAt),
                type: messageType,
              };
              break;
            case 'proposal':
              messageProps = {
                id: msg.id,
                sender: (msg.senderId === currentUserId ? 'user' : 'other') as
                  | 'user'
                  | 'other',
                timestamp: formatDate(msg.createdAt),
                type: messageType,
                meetingTime: meetingTime,
                onAccept: () => onAcceptMeeting(msg.id),
                onDecline: () => onDeclineMeeting(msg.id),
              };
              break;
            case 'accepted':
              messageProps = {
                id: msg.id,
                sender: (msg.senderId === currentUserId ? 'user' : 'other') as
                  | 'user'
                  | 'other',
                timestamp: formatDate(msg.createdAt),
                type: messageType,
                meetingTime: meetingTime,
                meetingId: msg.id,
                userRole: (msg.senderId === currentUserId
                  ? 'offer_creator'
                  : 'need_creator') as 'need_creator' | 'offer_creator',
              };
              break;
            case 'declined':
              messageProps = {
                id: msg.id,
                sender: (msg.senderId === currentUserId ? 'user' : 'other') as
                  | 'user'
                  | 'other',
                timestamp: formatDate(msg.createdAt),
                type: messageType,
                meetingTime: meetingTime,
              };
              break;
          }

          return <MessageBubble key={msg.id} {...messageProps} />;
        })
      ) : (
        // Show a simple centered text when no messages exist
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-500 text-sm">No message sent yet</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  messageBubble: {
    maxWidth: '75%',
    marginBottom: 12,
    padding: 12,
    borderRadius: 20,
  },
  userBubble: {
    alignSelf: 'flex-end',
    backgroundColor: '#0066ff',
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    alignSelf: 'flex-start',
    backgroundColor: '#f0f0f0',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
  },
  userText: {
    color: 'white',
  },
  otherText: {
    color: '#333',
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
  },
  userTimestamp: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'right',
  },
  otherTimestamp: {
    color: '#999',
    textAlign: 'left',
  },
});
