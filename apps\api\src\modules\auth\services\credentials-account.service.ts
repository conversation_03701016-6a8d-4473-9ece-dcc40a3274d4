import {
  credentialsAccountRepository,
  type CredentialsAccountCreateDto,
} from '@/modules/auth';
import type { UserSelectDto } from '@/modules/users/user.dto';

export const credentialsAccountService = {
  async createCredentialsAccount(data: CredentialsAccountCreateDto) {
    const newCredentialsAccount = await credentialsAccountRepository.create(
      data
    );

    return newCredentialsAccount;
  },

  async getUserCredentialsAccount(userId: UserSelectDto['id']) {
    const foundCredentialsAccount =
      await credentialsAccountRepository.findByUserId(userId);

    return foundCredentialsAccount;
  },
};
