import { useMutation, useQuery } from '@tanstack/react-query';
import {
  checkEmailExists,
  checkUserHasCredentials,
  signUp,
  sendOtp,
  verifyOtp,
  uploadMedia,
  type SignUpData,
  type OtpRequest,
  type OtpVerifyRequest,
} from './auth-api';

// Helper function to handle CatchErrorResult and throw errors for React Query
async function handleCatchErrorResult<T, E extends Error>(
  promise: Promise<[E | undefined, T | undefined]>
): Promise<T> {
  const [error, data] = await promise;
  if (error) {
    throw error;
  }
  return data!;
}

// Hook to check if an email exists
export function useCheckEmailExists() {
  return useMutation({
    mutationFn: (email: string) =>
      handleCatchErrorResult(checkEmailExists(email)),
  });
}

// Hook to check if a user has credentials
export function useCheckUserHasCredentials() {
  return useMutation({
    mutationFn: (email: string) =>
      handleCatchErrorResult(checkUserHasCredentials(email)),
  });
}

// Hook for user sign up
export function useSignUp() {
  return useMutation({
    mutationFn: (data: SignUpData) => handleCatchErrorResult(signUp(data)),
  });
}

// Hook to send OTP
export function useSendOtp() {
  return useMutation({
    mutationFn: (otpOptions: OtpRequest) =>
      handleCatchErrorResult(sendOtp(otpOptions)),
  });
}

// Hook to verify OTP
export function useVerifyOtp() {
  return useMutation({
    mutationFn: (otpOptions: OtpVerifyRequest) =>
      handleCatchErrorResult(verifyOtp(otpOptions)),
  });
}

// Hook to upload media
export function useUploadMedia() {
  return useMutation({
    mutationFn: (fileUri: string) => uploadMedia(fileUri),
  });
}
