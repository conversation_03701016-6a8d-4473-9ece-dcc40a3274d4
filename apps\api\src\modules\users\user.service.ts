import type { UserCreateDto, UserSelectDto, UserUpdateDto } from './user.dto';
import { userRepository } from './user.repository';

export const userService = {
  async createUser(data: UserCreateDto) {
    const newUser = await userRepository.create(data);

    return newUser;
  },

  async getUserById(id: UserSelectDto['id']) {
    const foundUser = await userRepository.findById(id);

    return foundUser;
  },

  async getUserByEmail(email: string) {
    const foundUser = await userRepository.findByEmail(email);

    return foundUser;
  },

  async updateById(id: UserSelectDto['id'], data: UserUpdateDto) {
    return await userRepository.updateById(id, data);
  },
};
