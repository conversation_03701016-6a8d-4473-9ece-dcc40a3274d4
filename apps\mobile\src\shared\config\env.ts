import { z } from 'zod';

const EnvSchema = z.object({
  // Node environment
  NODE_ENV: z.string().default('development'),
  // Backend API
  EXPO_PUBLIC_WEB_BACKEND_API_URL: z.string().url(),
  EXPO_PUBLIC_MOBILE_BACKEND_API_URL: z.string().min(1),
  EXPO_PUBLIC_ENCRYPTED_GOOGLE_MAPS_API_KEY: z.string().min(1),
});

// eslint-disable-next-line ts/no-redeclare
const { data: env, error } = EnvSchema.safeParse(process.env);

if (error) {
  console.error('❌ Invalid env:');
  console.error(JSON.stringify(error.flatten().fieldErrors, null, 2));
  process.exit(1);
}

export default env!;
