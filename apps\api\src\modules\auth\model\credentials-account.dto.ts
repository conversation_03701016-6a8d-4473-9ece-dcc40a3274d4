import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod';

import { credentialsAccounts } from '@needit/db';

export const credentialsAccountCreateDto = createInsertSchema(
  credentialsAccounts
).pick({
  userId: true,
  passwordHash: true,
});
export type CredentialsAccountCreateDto = z.infer<
  typeof credentialsAccountCreateDto
>;

export const credentialsAccountSelectDto = createSelectSchema(
  credentialsAccounts
).omit({
  passwordHash: true,
});
export type CredentialsAccountSelectDto = z.infer<
  typeof credentialsAccountSelectDto
>;

export const credentialsAccountUpdateDto = createUpdateSchema(
  credentialsAccounts
).pick({
  passwordHash: true,
});
export type CredentialsAccountUpdateDto = z.infer<
  typeof credentialsAccountUpdateDto
>;
