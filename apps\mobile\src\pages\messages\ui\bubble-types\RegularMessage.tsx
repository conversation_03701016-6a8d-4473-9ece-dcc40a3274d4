import React from 'react';
import { View } from 'react-native';
import { Text } from '@/shared/ui';
import { RegularMessageProps } from '../types/message-types';

function RegularMessage(props: RegularMessageProps) {
  const { id, text, sender, timestamp } = props;
  return (
    <View
      className={`mb-3 max-w-[75%] ${
        sender === 'user' ? 'self-end' : 'self-start'
      }`}
    >
      <View
        className={`rounded-3xl px-4 py-3 ${
          sender === 'user'
            ? 'bg-blue-600 rounded-br-lg'
            : 'bg-gray-100 rounded-bl-lg'
        }`}
      >
        <Text
          className={`${
            sender === 'user' ? 'text-white' : 'text-gray-900'
          } text-[15px]`}
        >
          {text}
        </Text>
      </View>
      <Text
        className={`text-xs text-gray-400 mt-1 ${
          sender === 'user' ? 'text-right mr-1' : 'text-left ml-1'
        }`}
      >
        {timestamp}
      </Text>
    </View>
  );
}

export default RegularMessage;
