import React from 'react';
import { View } from 'react-native';
import { Text, VStack } from '@/shared/ui';
import QRCode from 'react-native-qrcode-svg';

interface MeetingQRCodeProps {
  meetingId: string;
  meetingTime: string;
  timestamp: string;
  userRole?: 'need_creator' | 'offer_creator';
}

export function MeetingQRCode({ 
  meetingId, 
  meetingTime, 
  timestamp,
  userRole = 'need_creator' 
}: MeetingQRCodeProps) {
  console.log('Rendering QR code with role:', userRole);
  
  // Generate a unique meeting data string that includes the meeting ID, time, and role
  const meetingData = JSON.stringify({
    id: meetingId,
    time: meetingTime,
    role: userRole,
    created: new Date().toISOString(),
  });

  // Different colors based on user role
  const qrColor = userRole === 'need_creator' ? '#1D4ED8' : '#047857'; // Blue for need creator, Green for offer creator
  const headerBgColor = userRole === 'need_creator' ? 'bg-blue-600' : 'bg-green-600';
  const bgColor = userRole === 'need_creator' ? 'bg-blue-50' : 'bg-green-50';
  const borderColor = userRole === 'need_creator' ? 'border-blue-100' : 'border-green-100';
  const textColor = userRole === 'need_creator' ? 'text-blue-600' : 'text-green-600';

  // Different text based on user role
  const roleText = userRole === 'need_creator' 
    ? "You created this need. Show this QR code to verify your identity." 
    : "You made an offer. Show this QR code to verify your identity.";

  return (
    <View className="mb-3 self-center max-w-[85%]">
      {/* QR Code Block with colored background */}
      <View className={`rounded-lg overflow-hidden ${bgColor} border ${borderColor}`}>
        {/* Header with colored background */}
        <View className={`${headerBgColor} px-3 py-2 flex-row justify-between items-center`}>
          <Text className="text-white">Meeting Confirmed</Text>
          <Text className="text-white">{meetingTime}</Text>
        </View>
        
        {/* QR Code */}
        <View className="px-3 py-4 items-center">
          <QRCode
            value={meetingData}
            size={150}
            color={qrColor}
            backgroundColor="#EFF6FF"
          />
          <Text className={`${textColor} text-center mt-3 font-medium`}>
            {roleText}
          </Text>
        </View>
      </View>
      <Text className="text-xs text-gray-400 mt-1 text-center">
        {timestamp}
      </Text>
    </View>
  );
}
