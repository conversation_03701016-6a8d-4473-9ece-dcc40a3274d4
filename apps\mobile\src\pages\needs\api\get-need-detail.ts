import { Need } from '@/pages/needs';

import { fetchJson } from '@/shared/lib';
import { User } from '@/shared/model';

type NeedDetail = Need & { user: User };

export async function getNeedDetail(needId: string): Promise<NeedDetail> {
  const [error, data] = await fetchJson<{ need: NeedDetail }>(
    `/api/needs/${needId}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (error) {
    throw error;
  }

  return data.need;
}
