import { Need } from '@/pages/needs';

import { nativeFetch } from '@/shared/lib';
import { User } from '@/shared/model';

type NeedDetail = Need & { user: User };

export async function getNeedDetail(needId: string) {
  const response = await nativeFetch(`/api/needs/${needId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch need detail');
  }

  const data = await response.json();
  return data.need as NeedDetail;
}
