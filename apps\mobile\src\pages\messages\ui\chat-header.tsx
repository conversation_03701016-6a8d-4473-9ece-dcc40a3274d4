import React from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { HStack, Text, VStack, Icon, ArrowLeftIcon } from '@/shared/ui';

interface ChatHeaderProps {
  firstName: string;
  lastName: string;
  imageUrl: string;
  isOnline: boolean;
  onBack?: () => void;
}

export function ChatHeader({ firstName, lastName, imageUrl, isOnline, onBack }: ChatHeaderProps) {
  return (
    <View className="bg-white px-4 py-3 border-b border-gray-100">
      <HStack space="sm" className="items-center justify-start w-full">
        {onBack && (
          <TouchableOpacity onPress={onBack} className="mr-2">
            <HStack space="xs" className="items-center">
              <Icon as={ArrowLeftIcon} size="sm" />
              <Text className="text-sm">Back</Text>
            </HStack>
          </TouchableOpacity>
        )}
        <View className="mr-4 relative">
          <Image 
            source={{ uri: imageUrl }} 
            className="w-12 h-12 rounded-full"
          />
          <View 
            className="absolute bottom-0 right-0 w-3.5 h-3.5 rounded-full border-2 border-white"
            style={{ backgroundColor: isOnline ? '#22C55E' : '#EF4444' }}
          />
        </View>
        <VStack space="xs">
          <Text className="font-semibold text-lg">
            {firstName} {lastName}
          </Text>
          <Text className="text-sm text-gray-400">
            {isOnline ? 'Online' : 'Offline'}
          </Text>
        </VStack>
      </HStack>
    </View>
  );
}
