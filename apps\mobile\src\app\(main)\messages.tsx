import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  VStack,
  Text,
  Icon,
  ArrowLeftIcon,
  Button,
} from '@/shared/ui';
import { ScrollView, View } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Chat<PERSON>eader, OfferInfoHeader, MeetingQRCode, MessageBubble, MessageInput } from '@/pages/messages';

interface MessageUI {
  id: string;
  text: string;
  sender: 'user' | 'other';
  timestamp: string;
  isMeetingProposal?: boolean;
  meetingTime?: string;
  isAccepted?: boolean;
}

interface Offer {
  id: string;
  title: string;
  description: string;
  price: string;
  status: string;
  location: string;
}

export default function Messages() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const offerId = (params.id as string) || '1';

  const handleBack = () => {
    router.back();
  };

  const [message, setMessage] = React.useState('');
  const [hasProposedMeeting, setHasProposedMeeting] = React.useState(false);
  const [userMessageCount, setUserMessageCount] = React.useState(0);
  const [userRole, setUserRole] = React.useState<'sender' | 'receiver'>('sender');
  const [messages, setMessages] = React.useState<MessageUI[]>([
    {
      id: '1',
      text: 'Hello, is this item still available?',
      sender: 'user',
      timestamp: '10:30',
    },
    {
      id: '2',
      text: 'Yes, it is! Are you interested?',
      sender: 'other',
      timestamp: '10:32',
    },
    {
      id: '3',
      text: 'Great! Can we meet tomorrow to see it?',
      sender: 'user',
      timestamp: '10:33',
    },
    {
      id: '4',
      text: 'Sure, what time works for you?',
      sender: 'other',
      timestamp: '10:35',
    },
  ]);

  const [offer] = React.useState<Offer>({
    id: '1',
    title: 'Vintage Bicycle',
    description: 'Great condition, barely used',
    price: '120',
    status: 'Available',
    location: 'Paris',
  });

  const [otherUser] = React.useState({
    firstName: 'John',
    lastName: 'Doe',
    imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
    isOnline: true,
  });

  const scrollViewRef = React.useRef<ScrollView>(null);

  React.useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: false });
  }, [messages]); // Add messages as a dependency to scroll when messages change

  // Function to handle the "Make an Offer" button click
  const handleMakeOffer = () => {
    try {
      console.log('handleMakeOffer called in messages.tsx');
      
      // Get current message count for debugging
      console.log('Current message count:', messages.length);
      
      const currentTime = new Date();
      const meetingTime = new Date(currentTime.getTime() + 60 * 60 * 1000);
      const formattedTime = meetingTime.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      // Create a new meeting proposal message with a unique ID
      const messageId = 'proposal-' + Date.now().toString();
      const newMessage: MessageUI = {
        id: messageId,
        text: 'I would like to propose a meeting.',
        sender: 'user',
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        isMeetingProposal: true,
        meetingTime: formattedTime,
      };

      console.log('Adding proposal message:', JSON.stringify(newMessage));
      
      // Add the message to the messages array using a callback to ensure we're working with the latest state
      setMessages(prevMessages => {
        console.log('Previous messages count:', prevMessages.length);
        const updatedMessages = [...prevMessages, newMessage];
        console.log('New messages count:', updatedMessages.length);
        return updatedMessages;
      });
      
      setHasProposedMeeting(true);
      
      // Ensure we're in sender mode
      setUserRole('sender');
      
      // Scroll to the end of the messages
      setTimeout(() => {
        if (scrollViewRef.current) {
          console.log('Scrolling to end');
          scrollViewRef.current.scrollToEnd({ animated: true });
        } else {
          console.log('ScrollView ref is null');
        }
      }, 100);
      
      // Show an alert for debugging
      Alert.alert(
        'Meeting Proposal Added',
        `Added message with ID: ${messageId}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error in handleMakeOffer:', error);
      Alert.alert('Error', 'An error occurred while creating the meeting proposal.');
    }
  };

  const handleSend = () => {
    if (!message.trim()) return;

    const newMessage: MessageUI = {
      id: Date.now().toString(),
      text: message.trim(),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };

    setMessages((prev) => [...prev, newMessage]);
    setMessage('');
    setUserMessageCount((prev) => prev + 1);

    setTimeout(() => {
      if (userMessageCount === 1 && !hasProposedMeeting) {
        const currentTime = new Date();
        const meetingTime = new Date(currentTime.getTime() + 60 * 60 * 1000);
        const formattedTime = meetingTime.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        });

        const proposalMessage: MessageUI = {
          id: (Date.now() + 1).toString(),
          text: 'I would like to propose a meeting.',
          sender: 'other',
          timestamp: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          isMeetingProposal: true,
          meetingTime: formattedTime,
        };

        setMessages((prev) => [...prev, proposalMessage]);
        setHasProposedMeeting(true);
      } else {
        const responseMessage: MessageUI = {
          id: (Date.now() + 1).toString(),
          text: "Thanks for your message! I'll get back to you soon.",
          sender: 'other',
          timestamp: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
        };

        setMessages((prev) => [...prev, responseMessage]);
      }
    }, 1000);
  };

  const handleAcceptMeeting = (messageId: string) => {
    console.log('Accepting meeting with ID:', messageId);
    
    // Find the original meeting proposal message
    const proposalMessage = messages.find(msg => msg.id === messageId);
    if (!proposalMessage) {
      console.error('Could not find meeting proposal message with ID:', messageId);
      Alert.alert('Error', 'Could not find the meeting proposal.');
      return;
    }
    
    console.log('Found proposal message:', proposalMessage);
    
    // Update the message to mark it as accepted
    setMessages((prev) =>
      prev.map((msg) => {
        if (msg.id === messageId && msg.isMeetingProposal) {
          console.log('Marking message as accepted:', msg.id);
          return { ...msg, isAccepted: true };
        }
        return msg;
      })
    );

    // Switch back to sender view to show the accepted meeting
    setUserRole('sender');
    
    // Show confirmation
    Alert.alert(
      'Meeting Accepted', 
      'You have accepted the meeting proposal. A QR code has been generated for the meeting.',
      [{ text: 'OK' }]
    );
    
    // Scroll to show the QR code
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, 500);
  };

  const handleDeclineMeeting = (messageId: string) => {
    console.log('Declining meeting with ID:', messageId);
    
    // Find the original meeting proposal message
    const proposalMessage = messages.find(msg => msg.id === messageId);
    if (!proposalMessage) {
      console.error('Could not find meeting proposal message with ID:', messageId);
      Alert.alert('Error', 'Could not find the meeting proposal.');
      return;
    }
    
    console.log('Found proposal message to decline:', proposalMessage);
    
    // Update the message to mark it as declined
    setMessages(prevMessages => 
      prevMessages.map(msg => {
        if (msg.id === messageId) {
          console.log('Marking message as declined:', msg.id);
          return {
            ...msg,
            isDeclined: true
          };
        }
        return msg;
      })
    );
    
    // Show confirmation to the user
    Alert.alert(
      'Meeting Declined', 
      'You have declined the meeting proposal. The other user can still send new proposals.',
      [{ text: 'OK' }]
    );
    
    // Scroll to show the declined message
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, 500);
  };

  // Add a function to manually add a meeting proposal for testing
  const addTestMeetingProposal = () => {
    const currentTime = new Date();
    const meetingTime = new Date(currentTime.getTime() + 60 * 60 * 1000);
    const formattedTime = meetingTime.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });

    const testMessage: MessageUI = {
      id: 'test-' + Date.now().toString(),
      text: 'Test meeting proposal',
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
      isMeetingProposal: true,
      meetingTime: formattedTime,
    };

    setMessages(prev => [...prev, testMessage]);
    setHasProposedMeeting(true);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      className="bg-gray-50"
    >
      <ChatHeader
        firstName={otherUser.firstName}
        lastName={otherUser.lastName}
        imageUrl={otherUser.imageUrl}
        isOnline={otherUser.isOnline}
        onBack={handleBack}
      />

      {offer && (
        <OfferInfoHeader
          title={offer.title}
          description={offer.description}
          price={offer.price}
          status={offer.status}
          location={offer.location}
        />
      )}
      <VStack className="flex-1" space="md">
        {/* Demo controls - for testing only */}
        <View className="flex-row justify-center py-2 bg-gray-100">
          <Button
            size="sm"
            variant={userRole === 'sender' ? 'solid' : 'outline'}
            className={`mx-1 ${userRole === 'sender' ? 'bg-blue-600' : 'border-blue-600'}`}
            onPress={() => setUserRole('sender')}
          >
            <Text className={userRole === 'sender' ? 'text-white' : 'text-blue-600'}>Sender View</Text>
          </Button>
          <Button
            size="sm"
            variant={userRole === 'receiver' ? 'solid' : 'outline'}
            className={`mx-1 ${userRole === 'receiver' ? 'bg-blue-600' : 'border-blue-600'}`}
            onPress={() => setUserRole('receiver')}
          >
            <Text className={userRole === 'receiver' ? 'text-white' : 'text-blue-600'}>Receiver View</Text>
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="mx-1 border-blue-600"
            onPress={addTestMeetingProposal}
          >
            <Text className="text-blue-600">Test Proposal</Text>
          </Button>
        </View>
        {/* Messages list */}
        <ScrollView
          ref={scrollViewRef}
          className="flex-1 px-4"
          contentContainerStyle={{
            paddingTop: 20,
          }}
        >
          {messages.map((msg) => {
            // Déterminer le type de message
            let messageType: 'regular' | 'proposal' | 'accepted' | 'declined' = 'regular';
            let meetingTime = msg.meetingTime || '';
            let isDeclined = (msg as any).isDeclined || false;
            
            if (msg.isMeetingProposal) {
              if (msg.isAccepted) {
                messageType = 'accepted';
              } else if (isDeclined) {
                messageType = 'declined';
              } else {
                messageType = 'proposal';
              }
            }
            
            // Try to parse the content as JSON to check if it's a meeting proposal or a declined message
            if (messageType === 'regular' && typeof msg.text === 'string') {
              try {
                const content = JSON.parse(msg.text);
                if (content.type === 'meetingProposal') {
                  messageType = 'proposal';
                  meetingTime = content.time;
                } else if (content.type === 'meetingDeclined' && content.displayAsDeclined) {
                  // This is a declined message notification
                  console.log('Found decline notification message:', msg.id);
                  
                  // Skip rendering this message as it's just a notification
                  // We'll show the declined status on the original message
                  return null;
                }
              } catch (e) {
                // Not a JSON message, so not a meeting proposal or declined message
              }
            }
            
            // Préparer les props selon le type
            const baseProps = {
              id: msg.id,
              sender: msg.sender,
              timestamp: msg.timestamp,
              type: messageType,
            };
            
            let messageProps;
            
            switch (messageType) {
              case 'regular':
                messageProps = {
                  ...baseProps,
                  text: msg.text,
                };
                break;
              case 'proposal':
                messageProps = {
                  ...baseProps,
                  meetingTime: meetingTime,
                  onAccept: handleAcceptMeeting,
                  onDecline: handleDeclineMeeting,
                };
                break;
              case 'accepted':
                messageProps = {
                  ...baseProps,
                  meetingTime: meetingTime,
                  meetingId: msg.id,
                  userRole: msg.sender === 'other' ? 'need_creator' : 'offer_creator',
                };
                break;
              case 'declined':
                messageProps = {
                  ...baseProps,
                  meetingTime: meetingTime,
                };
                break;
            }
            
            return <MessageBubble key={msg.id} {...messageProps} />;
          })}
        </ScrollView>

        {/* Message input */}
        <MessageInput 
          message={message}
          setMessage={setMessage}
          onSend={handleSend}
          onMakeOffer={handleMakeOffer}
          showMakeOfferButton={userRole === 'receiver'} // Only show for receivers (offerors), not senders (need creators)
        />
      </VStack>
    </KeyboardAvoidingView>
  );
}
