import { <PERSON>, useRouter } from 'expo-router';
import { View } from 'react-native';

import { useState } from 'react';

import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { signInSchema, SocialSignIn } from '@/pages/auth';

import { nativeFetch } from '@/shared/lib';
import {
  AlertCircleIcon,
  Button,
  ButtonText,
  EyeIcon,
  EyeOffIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  Heading,
  HStack,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  LinkText,
  TextDivider,
  VStack,
} from '@/shared/ui';

export function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof signInSchema>>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const handleSignIn = async () => {
    const { email, password } = form.getValues();

    await nativeFetch('/api/auth/sign-in/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
      }),
      credentials: 'include',
    })
      .then((response) => response.json())
      .then(() => {
        router.push('/');
      })
      .catch((error) => {
        console.log(error);
      });
  };

  return (
    <VStack space="lg" className="mx-6">
      <VStack space="md">
        <Heading size="3xl" className="text-typography-900">
          Login
        </Heading>
        <Controller
          control={form.control}
          name="email"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined} size="md">
              <FormControlLabel>
                <FormControlLabelText className="text-typography-500">
                  Email
                </FormControlLabelText>
              </FormControlLabel>
              <Input className="min-w-full">
                <InputField
                  type="text"
                  {...field}
                  onChangeText={field.onChange}
                />
              </Input>
              <FormControlError>
                <FormControlErrorIcon as={AlertCircleIcon} />
                <FormControlErrorText>
                  {fieldState.error?.message}
                </FormControlErrorText>
              </FormControlError>
            </FormControl>
          )}
        />
        <Controller
          control={form.control}
          name="password"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined} size="md">
              <HStack className="justify-between">
                <FormControlLabel>
                  <FormControlLabelText className="text-typography-500">
                    Password
                  </FormControlLabelText>
                </FormControlLabel>
                <Link href="/sign-up">
                  <LinkText>Forgot your password?</LinkText>
                </Link>
              </HStack>
              <Input className="w-full">
                <InputField
                  type={showPassword ? 'text' : 'password'}
                  {...field}
                  onChangeText={field.onChange}
                />
                <InputSlot
                  className="pr-3"
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <InputIcon as={showPassword ? EyeIcon : EyeOffIcon} />
                </InputSlot>
              </Input>
              <FormControlError>
                <FormControlErrorIcon as={AlertCircleIcon} />
                <FormControlErrorText>
                  {fieldState.error?.message}
                </FormControlErrorText>
              </FormControlError>
            </FormControl>
          )}
        />
        <Button
          // className="ml-auto"
          className="bg-blue-600"
          onPress={form.handleSubmit(handleSignIn)}
          size="xl"
        >
          <ButtonText className="text-typography-0">Login</ButtonText>
        </Button>
      </VStack>
      <View className="mx-6">
        <TextDivider>OR</TextDivider>
      </View>
      <SocialSignIn />
    </VStack>
  );
}
