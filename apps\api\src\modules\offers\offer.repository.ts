import { eq, getTableColumns } from 'drizzle-orm';

import { db, schema } from '@needit/db';

import type { UserSelectDto } from '@/modules/users';

import { jsonBuildObject } from '@/shared/lib';

import type { OfferCreateDto, OfferSelectDto, OfferUpdateDto } from './offer.dto';

export const offerRepository = {
  async create(data: OfferCreateDto) {
    return await db
      .insert(schema.offers)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findByReceiverId(receiverId: UserSelectDto['id']) {
    // First get the offers with their needs
    const offers = await db
      .select({
        ...getTableColumns(schema.offers),
        need: getTableColumns(schema.needs),
      })
      .from(schema.offers)
      .leftJoin(schema.needs, eq(schema.offers.needId, schema.needs.id))
      .leftJoin(schema.users, eq(schema.needs.userId, schema.users.id))
      .where(eq(schema.users.id, receiverId));
    
    // Then get the offer creators' user information for each offer
    const offersWithUsers = await Promise.all(
      offers.map(async (offer) => {
        const offerCreator = await db
          .select(getTableColumns(schema.users))
          .from(schema.users)
          .where(eq(schema.users.id, offer.userId))
          .then((res) => res[0]);
        
        return {
          ...offer,
          user: offerCreator,
        };
      })
    );
    
    return offersWithUsers;
  },

  async findByUserId(userId: UserSelectDto['id']) {
    return await db
      .select({
        ...getTableColumns(schema.offers),
        need: getTableColumns(schema.needs),
      })
      .from(schema.offers)
      .leftJoin(schema.needs, eq(schema.offers.needId, schema.needs.id))
      .where(eq(schema.offers.userId, userId));
  },

  async findByNeedId(needId: string) {
    return await db
      .select(getTableColumns(schema.offers))
      .from(schema.offers)
      .where(eq(schema.offers.needId, needId));
  },

  async findById(id: OfferSelectDto['id']) {
    // First get the offer with its need and need creator user
    const offer = await db
      .select({
        ...getTableColumns(schema.offers),
        need: {
          ...getTableColumns(schema.needs),
          user: jsonBuildObject(getTableColumns(schema.users)),
        },
      })
      .from(schema.offers)
      .leftJoin(schema.needs, eq(schema.offers.needId, schema.needs.id))
      .leftJoin(schema.users, eq(schema.needs.userId, schema.users.id))
      .where(eq(schema.offers.id, id))
      .then((res) => res[0]);
    
    if (!offer) {
      return null;
    }
    
    // Then get the offer creator's user information
    const offerCreator = await db
      .select(getTableColumns(schema.users))
      .from(schema.users)
      .where(eq(schema.users.id, offer.userId))
      .then((res) => res[0]);
    
    // Then get the latest message for this offer, if any
    const messages = await db
      .select(getTableColumns(schema.messages))
      .from(schema.messages)
      .where(eq(schema.messages.offerId, id))
      .orderBy(schema.messages.createdAt)
      .limit(1);
    
    // Add the offer creator and latest message to the offer
    return {
      ...offer,
      user: offerCreator,
      latestMessage: messages.length > 0 ? messages[0] : null,
    };
  },

  async updateById(id: OfferSelectDto['id'], data: Partial<OfferUpdateDto>) {
    return await db
      .update(schema.offers)
      .set(data)
      .where(eq(schema.offers.id, id))
      .returning()
      .then((res) => res[0]);
  },
};
