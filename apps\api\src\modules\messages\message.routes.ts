import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

import { messageService } from './message.service';
import { messageCreateDto } from './message.dto';

import type { AuthVariables } from '@/shared/model';

export const messageRoutes = new Hono<{
  Variables: AuthVariables;
}>()
  .get(
    '/offer/:offerId',
    zValidator('param', z.object({ offerId: z.string().uuid() })),
    async (c) => {
      const { offerId } = c.req.valid('param');

      // Get the session from the context
      const session = c.get('session')!;

      // Get the user from the session
      const user = session.user;

      const messages = await messageService.getMessagesByOfferId(offerId);

      return c.json({ messages });
    }
  )
  .post(
    '/',
    zValidator('json', messageCreateDto.omit({ senderId: true })),
    async (c) => {
      const body = c.req.valid('json');

      // Get the session from the context
      const session = c.get('session')!;

      // Get the user from the session
      const user = session.user;

      const message = await messageService.sendMessage({
        ...body,
        senderId: user.id,
      });

      return c.json({ message });
    }
  )
  .patch(
    '/:messageId/read',
    zValidator('param', z.object({ messageId: z.string().uuid() })),
    async (c) => {
      const { messageId } = c.req.valid('param');

      // Get the session from the context
      const session = c.get('session')!;

      // Get the user from the session
      const user = session.user;

      const message = await messageService.markMessageAsRead(messageId);

      return c.json({ message });
    }
  );
