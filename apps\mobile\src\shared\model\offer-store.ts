import { create } from 'zustand';
import { 
  createOffer, 
  getIncomingOffers,
  getOfferById, 
  getOutgoingOffers, 
  updateOffer as apiUpdateOffer,
  type Offer,
  type OfferDetail,
  type CreateOfferDto,
  type UpdateOfferDto
} from '@/shared/api';

interface OfferState {
  incomingOffers: Offer[];
  outgoingOffers: Offer[];
  selectedOffer: OfferDetail | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchIncomingOffers: () => Promise<void>;
  fetchOutgoingOffers: () => Promise<void>;
  fetchOfferDetail: (offerId: string) => Promise<void>;
  addOffer: (offer: CreateOfferDto) => Promise<any>;
  updateOffer: (offerId: string, data: UpdateOfferDto) => Promise<void>;
  deleteOffer: (offerId: string) => Promise<void>;
  setSelectedOffer: (offer: OfferDetail | null) => void;
  reset: () => void;
}

export const useOfferStore = create<OfferState>((set, get) => ({
  incomingOffers: [],
  outgoingOffers: [],
  selectedOffer: null,
  isLoading: false,
  error: null,

  fetchIncomingOffers: async () => {
    set({ isLoading: true, error: null });
    try {
      const offers = await getIncomingOffers();
      set({ incomingOffers: offers, isLoading: false });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch incoming offers',
        isLoading: false,
      });
    }
  },

  fetchOutgoingOffers: async () => {
    set({ isLoading: true, error: null });
    try {
      const offers = await getOutgoingOffers();
      set({ outgoingOffers: offers, isLoading: false });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch outgoing offers',
        isLoading: false,
      });
    }
  },

  fetchOfferDetail: async (offerId) => {
    set({ isLoading: true, error: null });
    try {
      const offer = await getOfferById(offerId);
      set({ selectedOffer: offer, isLoading: false });
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch offer detail',
        isLoading: false,
      });
    }
  },

  addOffer: async (offerData) => {
    set({ isLoading: true, error: null });
    try {
      const newOffer = await createOffer(offerData);
      // We don't add the new offer to the offers array since it's a different type
      // Instead, we'll refresh the offers list when needed
      set({ isLoading: false });
      return newOffer;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to create offer',
        isLoading: false,
      });
      throw error;
    }
  },

  updateOffer: async (offerId, data) => {
    set({ isLoading: true, error: null });
    try {
      const updatedOffer = await apiUpdateOffer(offerId, data);
      
      set((state) => {
        // Update in the incoming offers array
        const updatedIncomingOffers = state.incomingOffers.map((offer) =>
          offer.id === offerId ? { ...offer, ...data } : offer
        );

        // Update in the outgoing offers array
        const updatedOutgoingOffers = state.outgoingOffers.map((offer) =>
          offer.id === offerId ? { ...offer, ...data } : offer
        );

        // Update the selected offer if it matches
        const updatedSelectedOffer =
          state.selectedOffer?.id === offerId
            ? { ...state.selectedOffer, ...data }
            : state.selectedOffer;

        return {
          incomingOffers: updatedIncomingOffers,
          outgoingOffers: updatedOutgoingOffers,
          selectedOffer: updatedSelectedOffer,
          isLoading: false,
        };
      });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to update offer',
        isLoading: false,
      });
    }
  },

  deleteOffer: async (offerId) => {
    set({ isLoading: true, error: null });
    try {
      // Implement API call when available
      // For now, delete locally
      set((state) => {
        const filteredIncomingOffers = state.incomingOffers.filter(
          (offer) => offer.id !== offerId
        );
        
        const filteredOutgoingOffers = state.outgoingOffers.filter(
          (offer) => offer.id !== offerId
        );
        
        const updatedSelectedOffer =
          state.selectedOffer?.id === offerId ? null : state.selectedOffer;

        return {
          incomingOffers: filteredIncomingOffers,
          outgoingOffers: filteredOutgoingOffers,
          selectedOffer: updatedSelectedOffer,
          isLoading: false,
        };
      });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete offer',
        isLoading: false,
      });
    }
  },

  setSelectedOffer: (offer) => {
    set({ selectedOffer: offer });
  },

  reset: () => {
    set({ 
      incomingOffers: [], 
      outgoingOffers: [],
      selectedOffer: null, 
      isLoading: false, 
      error: null 
    });
  },
}));
