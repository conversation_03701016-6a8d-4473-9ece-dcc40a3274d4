import { createMiddleware } from 'hono/factory';
import { HTTPException } from 'hono/http-exception';

import type { AuthVariables } from '@/shared/model';
import status from 'http-status';

export const requireAuthMiddleware = createMiddleware<{
  Variables: AuthVariables;
}>(async (c, next) => {
  const session = c.get('session');

  if (!session) {
    throw new HTTPException(status.UNAUTHORIZED, {
      res: Response.json({
        error: {
          code: status[`${status.UNAUTHORIZED}_NAME`],
          message: 'Unauthorized Access: Token is missing',
        },
      }),
    });
  }

  await next();
});
