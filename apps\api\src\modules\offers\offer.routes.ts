import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';

import {
  offerSelectDto,
  offerService,
  offerUpdateDto,
  offerWithMessageCreateDto,
} from '@/modules/offers';

import type { AuthVariables } from '@/shared/model';

export const offerRoutes = new Hono<{
  Variables: AuthVariables;
}>()
  .post(
    '/',
    zValidator('json', offerWithMessageCreateDto.omit({ userId: true })),
    async (c) => {
      const body = c.req.valid('json');

      // Get the session from the context
      const session = c.get('session')!;

      // Get the user from the session
      const user = session.user;

      const offer = await offerService.createOffer({
        ...body,
        userId: user.id,
      });

      return c.json({ offer });
    }
  )
  .get('/incoming', async (c) => {
    // Get the session from the context
    const session = c.get('session')!;

    // Get the user from the session
    const user = session.user;

    const offers = await offerService.getIncomingOffers(user.id);

    return c.json({ offers });
  })
  .get('/outgoing', async (c) => {
    // Get the session from the context
    const session = c.get('session')!;

    // Get the user from the session
    const user = session.user;

    const offers = await offerService.getOutgoingOffers(user.id);

    return c.json({ offers });
  })
  .get(
    '/:id',
    zValidator('param', offerSelectDto.pick({ id: true })),
    async (c) => {
      const id = c.req.param('id');

      const offer = await offerService.getOfferById(id);

      return c.json({ offer });
    }
  )
  .patch(
    '/:id',
    zValidator('param', offerSelectDto.pick({ id: true })),
    zValidator('json', offerUpdateDto.partial()),
    async (c) => {
      const id = c.req.param('id');
      const body = c.req.valid('json');

      const updatedOffer = await offerService.updateOffer(id, body);

      return c.json({ offer: updatedOffer });
    }
  );
