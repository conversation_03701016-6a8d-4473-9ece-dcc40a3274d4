import React from 'react';
import { View } from 'react-native';
import { Text, VStack, HStack, Icon, ClockIcon } from '@/shared/ui';

interface OfferInfoHeaderProps {
  title: string;
  description: string;
  price: string;
  status: string;
  location: string;
}

export function OfferInfoHeader({ title, description, price, status, location }: OfferInfoHeaderProps) {
  return (
    <View className="bg-blue-50 px-4 py-3">
      <HStack className="justify-between">
        <VStack space="xs">
          <Text className="font-semibold text-base text-blue-900">
            {title}
          </Text>
          <Text className="text-sm text-blue-600">
            {description}
          </Text>
        </VStack>
        <VStack space="xs" className="items-end">
          <Text className="font-bold text-base text-blue-900">
            {price}€
          </Text>
          <HStack space="xs" className="items-center">
            <Icon as={ClockIcon} className="w-3 h-3 text-blue-500" />
            <Text className="text-xs text-blue-500">
              {location} • {status}
            </Text>
          </HStack>
        </VStack>
      </HStack>
    </View>
  );
}
