{"name": "@needit/mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "dotenv -v DARK_MODE=media -- expo start -c", "reset-project": "node ./scripts/reset-project.js", "android": "dotenv -v DARK_MODE=media -- expo start --android", "ios": "dotenv -v DARK_MODE=media -- expo start --ios", "web": "dotenv -v DARK_MODE=media -- expo start --web", "test": "jest --watchAll", "lint": "expo lint", "ui": "pnpx gluestack-ui"}, "dependencies": {"@babel/runtime": "^7.26.7", "@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.20", "@gluestack-ui/pin-input": "^0.0.12", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@hookform/resolvers": "^4.1.2", "@legendapp/motion": "^2.4.0", "@needit/socket": "workspace:^", "@react-native-aria/focus": "^0.2.9", "@react-native-aria/interactions": "^0.2.16", "@react-native-aria/overlays": "^0.3.15", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@stripe/stripe-react-native": "0.45.0", "@tanstack/react-query": "^5.66.9", "@teovilla/react-native-web-maps": "^0.9.5", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-native": "0.79.2", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.20.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "set-cookie-parser": "^2.7.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.14", "@types/react-test-renderer": "^18.3.0", "@types/set-cookie-parser": "^2.4.10", "babel-plugin-module-resolver": "^5.0.2", "dotenv-cli": "^8.0.0", "jest-expo": "53.0.5", "jscodeshift": "0.15.2", "react-test-renderer": "18.3.1", "typescript": "^5.8.3"}, "private": true}