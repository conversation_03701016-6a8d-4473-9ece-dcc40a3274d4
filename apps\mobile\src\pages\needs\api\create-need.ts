import { Need } from '@/pages/needs';

import { nativeFetch } from '@/shared/lib';

type CreateNeedDto = {
  title: string;
  description: string;
  categoryId: string;
  location: {
    x: number;
    y: number;
  };
};

export async function createNeed(need: CreateNeedDto) {
  console.log('Creating Need:', need);

  const response = await nativeFetch('/api/needs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(need),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not create need');
  }

  const data = await response.json();
  return data.need as Need;
}
