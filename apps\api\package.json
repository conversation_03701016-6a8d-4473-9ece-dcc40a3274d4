{"name": "@needit/api", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:db:up": "docker compose -f docker-compose.test.yaml up -d", "test:db:down": "docker compose -f docker-compose.test.yaml down"}, "dependencies": {"@hono/node-server": "^1.13.7", "@hono/zod-validator": "^0.4.3", "@needit/db": "workspace:^", "@needit/socket": "workspace:^", "@node-rs/argon2": "^2.0.2", "@supabase/supabase-js": "^2.49.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "hono": "^4.6.19", "http-status": "^2.1.0", "pg": "^8.13.1", "pino": "^9.6.0", "resend": "^4.1.2", "socket.io": "^4.8.1", "stripe": "^18.1.1", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.11.17", "@types/pg": "^8.11.11", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.30.4", "pino-pretty": "^13.0.0", "tsx": "^4.7.1", "vitest": "^3.1.1"}}