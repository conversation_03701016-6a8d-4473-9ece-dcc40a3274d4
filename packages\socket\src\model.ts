export interface ServerToClientEvents {
  newOffer: ({
    offerId,
    needId,
    needTitle,
    userId,
  }: {
    offerId: string;
    needId: string;
    needTitle: string;
    userId: string;
  }) => void;
  newMessage: ({
    senderFirstName,
    senderLastName,
    content,
  }: {
    senderFirstName: string;
    senderLastName: string;
    content: string;
  }) => void;
}

export interface ClientToServerEvents {
  typing: ({
    senderId,
    receiverId,
  }: {
    senderId: string;
    receiverId: string;
  }) => void;
}
