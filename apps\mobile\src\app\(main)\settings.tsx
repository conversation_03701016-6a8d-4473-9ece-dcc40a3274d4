import React, { useState } from 'react';
import { View, Text, Switch, TouchableOpacity } from 'react-native';
import { useColorScheme } from 'react-native';
import { useRouter } from 'expo-router';

const Settings = () => {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [paymentMethod, setPaymentMethod] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState(false);

  const handleSelectPayment = (method: string) => {
    setPaymentMethod(method);
  };

  return (
    <View className="flex-1 bg-white px-4 pt-12 ${darkMode ? 'bg-black' : 'bg-white'}`}>">
      <View className="flex-row items-center mb-6">
        <TouchableOpacity onPress={() => router.back()}>
          <Text className="text-lg mr-4">←</Text>
        </TouchableOpacity>
        <Text className="text-xl font-bold">Settings</Text>
      </View>


      <Text className="text-base font-semibold mb-2">Payment Method</Text>
      <View className="rounded-xl border border-gray-200 mb-6">
        {[
          { label: 'Paypal', value: 'paypal' },
          { label: 'Credit Card', value: 'credit' },
          { label: 'Apple Pay', value: 'apple' },
        ].map(({ label, value }) => (
          <TouchableOpacity
            key={value}
            className="flex-row items-center justify-between px-4 py-4 border-b last:border-b-0 border-gray-200"
            onPress={() => handleSelectPayment(value)}
          >
            <View className="flex-row items-center">
              <Text className="text-base">{label}</Text>
            </View>
            <View className={`w-5 h-5 rounded-full border-2 ${paymentMethod === value ? 'border-blue-500 bg-blue-500' : 'border-gray-400'}`} />
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity className="flex-row items-center justify-between py-4 border-t border-b border-gray-200">
        <Text className="text-base">🌐 My choices</Text>
      </TouchableOpacity>

      <TouchableOpacity className="flex-row items-center justify-between py-4 border-b border-gray-200">
        <Text className="text-base">📦 Subscription</Text>
        <Text className="text-red-500 border border-red-500 px-3 py-1 rounded-md text-sm">✘ unsubscribe</Text>
      </TouchableOpacity>

      <View className="flex-row items-center justify-between py-4 border-b border-gray-200 opacity-50">
        <Text className="text-base">🌐 Language</Text>
        <Text className="text-sm">English (EN)</Text>
      </View>

      <View className="flex-row items-center justify-between py-4 border-b border-gray-200">
        <Text className="text-base">Dark mode</Text>
        <Switch value={darkMode} onValueChange={setDarkMode} />
      </View>

      <TouchableOpacity className="flex-row items-center justify-between py-4 border-b border-gray-200">
        <Text className="text-base text-red-600">Log Out</Text>
        <Text className="text-xl">↩️</Text>
      </TouchableOpacity>
    </View>
  );
};

export default Settings;
