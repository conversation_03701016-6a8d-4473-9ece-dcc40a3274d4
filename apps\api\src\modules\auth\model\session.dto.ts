import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

import { schema } from '@needit/db';

export const sessionCreateDto = createInsertSchema(schema.sessions).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type SessionCreateDto = z.infer<typeof sessionCreateDto>;

export const sessionSelectDto = createSelectSchema(schema.sessions).omit({
  tokenHash: true,
});
export type SessionSelectDto = z.infer<typeof sessionSelectDto>;
