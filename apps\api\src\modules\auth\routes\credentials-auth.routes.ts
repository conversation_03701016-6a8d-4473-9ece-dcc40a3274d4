import { Hono } from 'hono';
import { deleteCookie, setCookie } from 'hono/cookie';
import { HTTPException } from 'hono/http-exception';
import { getConnInfo } from '@hono/node-server/conninfo';
import { zValidator } from '@hono/zod-validator';

import status from 'http-status';
import {
  defaultSessionDuration,
  sessionTokenCookieName,
  sessionTokenCookieOptions,
} from '@/modules/auth/auth.config';
import {
  hashPassword,
  hashSessionToken,
  verifyPassword,
} from '@/modules/auth/auth.lib';
import {
  signInDto,
  signUpDto,
} from '@/modules/auth/model/credentials-auth.dto';
import { credentialsAccountService } from '@/modules/auth/services/credentials-account.service';
import { sessionService } from '@/modules/auth/services/session.service';
import { mediaRepository } from '@/modules/medias/media.repository';
import { userService } from '@/modules/users';

import { generateToken, getDateFromNow } from '@/shared/lib';
import type { AuthVariables } from '@/shared/model';

export const credentialsAuthRoutes = new Hono<{
  Variables: AuthVariables;
}>()
  .post('/sign-up/email', zValidator('json', signUpDto), async (c) => {
    const body = c.req.valid('json');

    const existingUser = await userService.getUserByEmail(body.email);
    if (existingUser) {
      throw new HTTPException(status.CONFLICT, {
        res: Response.json({
          error: {
            code: status[`${status.CONFLICT}_NAME`],
            message: 'User already exists',
          },
        }),
      });
    }

    const newUser = await userService.createUser({
      email: body.email,
      firstName: body.firstName,
      lastName: body.lastName,
    });

    const hashedPassword = await hashPassword(body.password);
    await credentialsAccountService.createCredentialsAccount({
      userId: newUser.id,
      passwordHash: hashedPassword,
    });

    // Update user with imageRef if provided
    if (body.imageRef) {
      const oldPath = body.imageRef;
      const newPath = `${newUser.id}/${oldPath.split('/').pop()}`;

      try {
        // Rename the file in Supabase storage
        await mediaRepository.renameMedia(oldPath, newPath);

        // Update user record with the new imageRef
        await userService.updateById(newUser.id, { imageRef: newPath });
      } catch (error) {
        // Handle errors (e.g., file not found, permission issues)
        console.error('Error updating user imageRef:', error);
        // Consider rolling back user creation if this fails
        throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
          res: Response.json({
            error: {
              code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
              message: 'Failed to update user image reference',
            },
          }),
        });
      }
    }

    return c.json({
      message: 'User created successfully',
      user: newUser,
    });
  })

  .post('/sign-in/email', zValidator('json', signInDto), async (c) => {
    const body = c.req.valid('json');

    const user = await userService.getUserByEmail(body.email);
    if (!user) {
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message: 'Invalid credentials',
          },
        }),
      });
    }

    const credentials =
      await credentialsAccountService.getUserCredentialsAccount(user.id);

    if (!credentials) {
      // User has an account but used a social login (no password defined yet)
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message:
              'No password defined for this user. Please use a social login',
          },
        }),
      });
    }

    const isPasswordCorrect = await verifyPassword(
      credentials.passwordHash,
      body.password
    );

    if (!isPasswordCorrect) {
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message: 'Invalid credentials',
          },
        }),
      });
    }

    const userAgent = c.req.header('User-Agent');
    const clientIp =
      c.req.header('X-Forwarded-For') || getConnInfo(c).remote.address;

    // TODO: Implement OTP if user is trying to logging from a different device or location

    const sessionToken = generateToken();
    const hashedToken = hashSessionToken(sessionToken);

    const expirationDate = getDateFromNow(defaultSessionDuration);

    const session = await sessionService.createSession({
      tokenHash: hashedToken,
      userId: user.id,
      ipAddress: clientIp,
      userAgent,
      expiresAt: expirationDate,
    });

    setCookie(c, sessionTokenCookieName, sessionToken, {
      ...sessionTokenCookieOptions,
      expires: session.expiresAt,
    });

    return c.json({
      message: 'Login successful',
      session,
    });
  });
