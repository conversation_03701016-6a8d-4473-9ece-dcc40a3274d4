import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod';

import { schema } from '@needit/db';

export const offerCreateDto = createInsertSchema(schema.offers).omit({
  id: true,
  status: true,
  createdAt: true,
  updatedAt: true,
});
export type OfferCreateDto = z.infer<typeof offerCreateDto>;

export const offerWithMessageCreateDto = offerCreateDto.extend({
  message: z.string(),
});
export type OfferWithMessageCreateDto = z.infer<
  typeof offerWithMessageCreateDto
>;

export const offerSelectDto = createSelectSchema(schema.offers);
export type OfferSelectDto = z.infer<typeof offerSelectDto>;

export const offerUpdateDto = createUpdateSchema(schema.offers).pick({
  price: true,
  status: true,
});
export type OfferUpdateDto = z.infer<typeof offerUpdateDto>;
