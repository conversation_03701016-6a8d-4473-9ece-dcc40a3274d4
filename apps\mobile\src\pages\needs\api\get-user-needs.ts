import type { Need } from '@/pages/needs';

import { fetchJson } from '@/shared/lib';
import { User } from '@/shared/model';

// Extended Need type that includes user information
export type NeedWithUser = Need & { user: User };

export async function getUserNeeds(): Promise<NeedWithUser[]> {
  const [error, data] = await fetchJson<{ needs: NeedWithUser[] }>(
    '/api/needs/user',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (error) {
    throw error;
  }

  return data.needs;
}
