import type { Need } from '@/pages/needs';

import { nativeFetch } from '@/shared/lib';
import { User } from '@/shared/model';

// Extended Need type that includes user information
export type NeedWithUser = Need & { user: User };

export async function getUserNeeds() {
  const response = await nativeFetch('/api/needs/user', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Could not fetch user needs');
  }

  const data = await response.json();
  return data.needs as NeedWithUser[];
}
