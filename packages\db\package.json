{"name": "@needit/db", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"drizzle-orm": "^0.39.1", "pg": "^8.13.1", "tslib": "^2.3.0"}, "devDependencies": {"@types/pg": "^8.11.11", "drizzle-kit": "^0.30.4"}}