import { beforeAll } from 'vitest';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js/driver';

import * as dotenv from 'dotenv';

import { db } from '@needit/db';

// Change the database environment variable for the test
dotenv.config({ path: '.env.test' });

// Before all tests
beforeAll(async () => {
  try {
    // Run migrations
    await migrate(db as unknown as PostgresJsDatabase<Record<string, never>>, {
      migrationsFolder: './drizzle',
    });
  } catch (error) {
    console.error('Error while creating tables', error);
    throw error;
  }
});
