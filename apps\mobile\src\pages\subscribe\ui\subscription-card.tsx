﻿import { Linking } from 'react-native';
import { useState } from 'react';

import { CheckCircle } from 'lucide-react-native';

import { subscribe } from '@/pages/subscribe';

import {
  Box,
  Button,
  ButtonText,
  Heading,
  HStack,
  VStack,
  Text,
  Icon,
  Switch,
} from '@/shared/ui';

const content = {
  monthly: {
    priceId: 'price_1RJElWRqAGveVCC5gEAH0asa',
    current: '$4.99 / mo',
  },
  yearly: {
    priceId: 'price_1RJEmsRqAGveVCC5jm6B18mD',
    old: '$59.99 / yr',
    current: '$24.99 / yr',
  },
};

const advantages = [
  'Advanced filtering options',
  'No more sampling when you want to interact with someone',
  'Increased visibility',
];

export function SubscriptionCard() {
  const [plan, setPlan] = useState<'monthly' | 'yearly'>('yearly');

  const handleSubscribe = async () => {
    try {
      const data = await subscribe({
        priceId: content[plan].priceId,
      });

      Linking.openURL(data.url);
    } catch (err) {
      console.error('Error:', err);
      alert('Stripe error');
    }
  };

  return (
    <Box className="bg-blue-300 p-6 rounded-2xl mx-4 mt-8 w-[90%] max-w-[400px] self-center">
      <HStack className="justify-between mb-4">
        <Text className="text-white">Monthly</Text>
        <Switch
          value={plan === 'yearly'}
          onValueChange={() =>
            setPlan((prev) => (prev === 'yearly' ? 'monthly' : 'yearly'))
          }
        />
        <Text className="text-white">Yearly</Text>
      </HStack>

      <Heading size="xl" className="text-white mb-2">
        Premium
      </Heading>

      {plan === 'yearly' ? (
        <>
          <Text className="line-through text-blue-500 text-lg">
            {content[plan].old}
          </Text>
          <Heading size="xl" className="text-blue-700 mt-1">
            {content[plan].current}
          </Heading>
        </>
      ) : (
        <Heading size="xl" className="text-blue-700">
          {content[plan].current}
        </Heading>
      )}

      <VStack className="mt-6 space-y-3 w-full">
        {advantages.map((item, i) => (
          <HStack key={i} className="items-center space-x-2">
            <Icon as={CheckCircle} size="md" className="text-white" />
            <Text className="text-white">{item}</Text>
          </HStack>
        ))}
      </VStack>

      <Button
        className="bg-blue-600 mt-6 rounded-lg w-full justify-center"
        onPress={handleSubscribe}
      >
        <ButtonText>Buy now</ButtonText>
      </Button>
    </Box>
  );
}
