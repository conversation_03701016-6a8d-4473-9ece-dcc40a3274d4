export interface PaymentCreateDto {
  userId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed';
  paymentMethodId?: string;
  paymentIntentId?: string;
  connectedAccountId?: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentSelectDto {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed';
  paymentMethodId?: string;
  paymentIntentId?: string;
  connectedAccountId?: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt?: Date;
}

export interface PaymentUpdateDto {
  status?: 'pending' | 'succeeded' | 'failed';
  paymentMethodId?: string;
  paymentIntentId?: string;
  metadata?: Record<string, unknown>;
}
