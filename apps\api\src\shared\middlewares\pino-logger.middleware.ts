import { createMiddleware } from 'hono/factory';

import * as crypto from 'node:crypto';

import { logger } from '@/shared/lib';

export const pinoLoggerMiddleware = createMiddleware(async (c, next) => {
  const startTime = Date.now();

  await next();

  const reqId = c.req.header('X-Request-Id') || crypto.randomUUID();
  const responseTime = Date.now() - startTime;

  const logData = {
    res: {
      status: c.res.status,
      headers: c.res.headers,
    },
    req: {
      method: c.req.method,
      url: c.req.url,
      headers: c.req.header(),
    },
    reqId,
    responseTime,
  };

  if (c.res.status >= 400) {
    logger.error(logData);
  } else {
    logger.info(logData);
  }
});
