import React, { useState } from 'react';
import { View, Platform, Alert, Text } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {
  Input,
  InputField,
  Button,
  HStack,
  Icon,
  PaperclipIcon,
  ArrowRightIcon,
  EditIcon,
  CalendarDaysIcon,
} from '@/shared/ui';

interface MessageInputProps {
  message: string;
  setMessage: (message: string) => void;
  onSend: () => void;
  onMakeOffer?: () => void;
  showMakeOfferButton?: boolean;
}

export function MessageInput({ 
  message, 
  setMessage, 
  onSend,
  onMakeOffer,
  showMakeOfferButton = true
}: MessageInputProps) {
  const [showOfferButton, setShowOfferButton] = useState(false);

  const handleGalleryPick = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission needed',
        'Gallery permission is required to select photos.'
      );
      return;
    }
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.8,
    });
    if (!result.canceled) {
      console.log('Gallery photo:', result.assets[0]);
    }
  };

  const handleCameraPick = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission needed',
        'Camera permission is required to take photos.'
      );
      return;
    }
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.8,
    });
    if (!result.canceled) {
      console.log('Camera photo:', result.assets[0]);
    }
  };

  const toggleOfferButton = () => {
    console.log('Toggle offer button clicked, current state:', showOfferButton);
    setShowOfferButton(!showOfferButton);
  };

  const handleMakeOffer = () => {
    console.log('Make offer button clicked, onMakeOffer exists:', !!onMakeOffer);
    if (onMakeOffer) {
      try {
        // Call the parent's onMakeOffer function
        onMakeOffer();
        
        // Hide the offer button after making an offer
        setShowOfferButton(false);
        
        // Show an alert to confirm the action
        Alert.alert(
          'Meeting Proposal Sent',
          'Your meeting proposal has been sent.',
          [{ text: 'OK' }]
        );
      } catch (error) {
        console.error('Error in handleMakeOffer:', error);
        Alert.alert('Error', 'Failed to send meeting proposal. Please try again.');
      }
    } else {
      console.error('onMakeOffer prop is not defined');
      Alert.alert('Error', 'Cannot make offer at this time.');
    }
  };

  return (
    <View className="py-3 px-2 bg-white border-t border-gray-100">
      <HStack space="sm" className="items-end px-2">
        {showMakeOfferButton && (
          <>
            {showOfferButton ? (
              <Button
                size="sm"
                variant="outline"
                className="rounded-full items-center justify-center p-2 mr-1 border-blue-600"
                onPress={handleMakeOffer}
              >
                <Icon className="text-blue-600" as={CalendarDaysIcon} size="sm" />
                <View className="ml-1">
                  <View className="text-blue-600 text-xs font-medium">Make an offer</View>
                </View>
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                className="rounded-full w-11 h-11 items-center justify-center p-0 mr-1 border-blue-600"
                onPress={toggleOfferButton}
              >
                <Text className="text-blue-600 text-xl font-bold">+</Text>
              </Button>
            )}
          </>
        )}
        <Button
          size="sm"
          variant="link"
          className="rounded-full w-11 h-11 items-center justify-center p-0"
          onPress={handleGalleryPick}
        >
          <Icon className="text-gray-500" as={PaperclipIcon} size="md" />
        </Button>
        <Button
          size="sm"
          variant="link"
          className="rounded-full w-11 h-11 items-center justify-center p-0 mr-1"
          onPress={handleCameraPick}
        >
          <Icon className="text-gray-500" as={EditIcon} size="md" />
        </Button>
        <Input className="flex-1 bg-gray-100" size="md" variant="rounded">
          <InputField
            placeholder="Type a message..."
            maxLength={1000}
            value={message}
            onChangeText={setMessage}
            multiline
            className="max-h-24 px-4 py-2 text-[15px]"
          />
        </Input>
        <Button
          size="sm"
          variant="solid"
          onPress={onSend}
          isDisabled={!message.trim()}
          className="rounded-full w-11 h-11 items-center justify-center p-0 ml-1 bg-blue-600"
        >
          <Icon className="text-white" as={ArrowRightIcon} size="md" />
        </Button>
      </HStack>
    </View>
  );
}
