import * as argon2 from '@node-rs/argon2';

import { sha256Hash } from '@/shared/lib';

// Hashing
export async function hashPassword(password: string) {
  return await argon2.hash(password);
}

export async function verifyPassword(password: string, hashedPassword: string) {
  return await argon2.verify(password, hashedPassword);
}

// Tokens
export function hashSessionToken(token: string) {
  return sha256Hash(token, 'hex') as string;
}
