import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';

import type { AuthVariables } from '@/shared/model';

import { needCreateDto, needSelectDto, needUpdateDto } from './need.dto';
import { needService } from './need.service';

export const needRoutes = new Hono<{
  Variables: AuthVariables;
}>();

needRoutes.post(
  '/',
  zValidator('json', needCreateDto.omit({ userId: true })),
  async (c) => {
    // Get the session from the context
    const session = c.get('session')!;

    // Get the user from the session
    const user = session.user;

    // Create the need for the user, using the user id
    const body = c.req.valid('json');

    const newNeed = await needService.createNeed({
      ...body,
      userId: user.id,
    });

    return c.json({ need: newNeed });
  }
);

needRoutes.get('/', async (c) => {
  const allNeeds = await needService.getAllNeeds();

  return c.json({ needs: allNeeds });
});

needRoutes.get('/user', async (c) => {
  // Get the session from the context
  const session = c.get('session')!;

  // Get the user from the session
  const user = session.user;

  // Get all needs for the user
  const userNeeds = await needService.getUserNeeds(user.id);

  return c.json({ needs: userNeeds });
});

needRoutes.get(
  '/:id',
  zValidator('param', needSelectDto.pick({ id: true })),
  async (c) => {
    const id = c.req.valid('param').id;

    const need = await needService.getNeed(id);

    return c.json({ need });
  }
);

needRoutes.patch(
  '/:id',
  zValidator('param', needSelectDto.pick({ id: true })),
  zValidator('json', needUpdateDto),
  async (c) => {
    const id = c.req.valid('param').id;
    const body = c.req.valid('json');

    const updatedNeed = await needService.updateNeed(id, body);

    return c.json({ need: updatedNeed });
  }
);

needRoutes.delete(
  '/:id',
  zValidator('param', needSelectDto.pick({ id: true })),
  async (c) => {
    const id = c.req.valid('param').id;

    const deletedNeed = await needService.deleteNeed(id);

    return c.json({ need: deletedNeed });
  }
);
