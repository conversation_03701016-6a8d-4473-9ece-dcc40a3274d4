import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { HTTPException } from 'hono/http-exception';

import status from 'http-status';

import { otpCreateDto, otpVerifyDto } from '@/modules/auth/model/otp.dto';
import { otpService } from '@/modules/auth/services/otp.service';

import type { AuthVariables } from '@/shared/model';

export const otpRoutes = new Hono<{
  Variables: AuthVariables;
}>()
  .post('/', zValidator('json', otpCreateDto), async (c) => {
    try {
      const body = c.req.valid('json');

      let userId = body.userId;

      // If userId is not provided, get it from the session (requires auth)
      if (!userId) {
        const session = c.get('session');
        if (!session) {
          throw new HTTPException(status.UNAUTHORIZED, {
            res: Response.json({
              error: {
                code: status[`${status.UNAUTHORIZED}_NAME`],
                message: 'Authentication required',
              },
            }),
          });
        }
        userId = session.user.id;
      }

      await otpService.sendOtpEmail(userId, body.type);

      return c.json({
        message: 'OTP sent successfully',
      });
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
        res: Response.json({
          error: {
            code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
            message:
              error instanceof Error ? error.message : 'Failed to send OTP',
          },
        }),
      });
    }
  })

  // Verify OTP
  .post('/verify', zValidator('json', otpVerifyDto), async (c) => {
    try {
      const body = c.req.valid('json');

      const isValid = await otpService.verifyOtp(
        body.userId,
        body.otp,
        body.type
      );

      // If OTP is for email verification, mark email as verified
      if (isValid && body.type === 'email-verification') {
        await otpService.markEmailAsVerified(body.userId);
      }

      return c.json({
        message: 'OTP verified successfully',
        verified: isValid,
      });
    } catch (error) {
      throw new HTTPException(status.BAD_REQUEST, {
        res: Response.json({
          error: {
            code: status[`${status.BAD_REQUEST}_NAME`],
            message:
              error instanceof Error ? error.message : 'Failed to verify OTP',
          },
        }),
      });
    }
  });
