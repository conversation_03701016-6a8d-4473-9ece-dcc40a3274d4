import React from 'react';
import { View } from 'react-native';
import { Text } from '@/shared/ui';
import { DeclinedMeetingProps } from '../types/message-types';

function DeclinedMeeting(props: DeclinedMeetingProps) {
  const { timestamp, meetingTime } = props;
  return (
    <View className="mb-3 self-center max-w-[85%]">
      <View className="rounded-lg overflow-hidden bg-red-50 border border-red-100">
        <View className="bg-red-600 px-3 py-2 flex-row justify-between items-center">
          <Text className="text-white">Meeting Proposal</Text>
          <Text className="text-white">{meetingTime || 'Time not set'}</Text>
        </View>
        <View className="px-3 py-2">
          <Text className="text-red-600 text-center font-medium">
            Offer declined
          </Text>
        </View>
      </View>
      <Text className="text-xs text-gray-400 mt-1 text-center">
        {timestamp}
      </Text>
    </View>
  );
}

export default DeclinedMeeting;
