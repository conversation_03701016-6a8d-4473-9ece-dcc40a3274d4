import { CatchErrorResult, nativeFetch, HttpError } from '@/shared/lib';
import { CustomFormData } from '@/shared/model';

interface SignUpCredentials {
  email: string;
  password: string;
}

interface SignUpProfile {
  firstName: string;
  lastName: string;
  imageRef?: string;
}

interface SignUpData extends SignUpCredentials, SignUpProfile {}

interface OtpRequest {
  type:
    | 'email-verification'
    | 'sign-in'
    | 'forget-password'
    | 'update-password';
  userId: string;
}

interface OtpVerifyRequest {
  userId: string;
  otp: string;
  type: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isEmailVerified: boolean;
  imageRef?: string;
  createdAt: string;
  updatedAt: string;
}

export type SignUpResponse = {
  message: string;
  user: User;
};

export type OtpResponse = {
  message: string;
};

export type OtpVerifyResponse = {
  message: string;
  user: User;
};

// Check if email is already used
export async function checkEmailExists(
  email: string
): Promise<CatchErrorResult<boolean>> {
  try {
    const [resErr, response] = await nativeFetch(`/api/users?email=${email}`, {
      method: 'HEAD',
      credentials: 'include',
    }).catch((error) => [error as Error, undefined] as const);

    if (resErr) return [resErr, undefined];

    return [undefined, response.ok];
  } catch (error) {
    return [error as Error, undefined];
  }
}

// Check if user has credentials (password)
export async function checkUserHasCredentials(
  email: string
): Promise<CatchErrorResult<boolean, HttpError | Error>> {
  // First, get the user ID from email
  const [userResErr, userResponse] = await nativeFetch<{
    user: { id: string };
  }>(`/api/users?email=${email}`, {
    method: 'GET',
    credentials: 'include',
  }).catch((error) => [error as Error, undefined] as const);

  if (userResErr) {
    if (userResErr instanceof HttpError) {
      if (userResErr.cause.status === 404) {
        return [undefined, false];
      }
    }
    return [userResErr, undefined];
  }

  const [jsonErr, data] = await userResponse
    .json()
    .catch((error) => [error as Error, undefined] as const);

  if (jsonErr) return [jsonErr, undefined];

  const userId = data.user.id;

  if (!userId) {
    return [undefined, false];
  }

  // Then check if the user has credentials
  const [credentialsErr, credentialsResponse] = await nativeFetch(
    `/api/users/${userId}/credentials`,
    {
      method: 'HEAD',
      credentials: 'include',
    }
  );

  if (credentialsResponse?.ok) {
    return [undefined, true];
  }

  return [undefined, false];
}

// Sign up with email and password
export async function signUp(data: SignUpData) {
  const [error, response] = await nativeFetch<SignUpResponse>(
    '/api/auth/sign-up/email',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
    }
  );

  if (error) {
    return [error, undefined];
  }

  return [undefined, response];
}

// Send OTP for email verification
export async function sendOtp(
  otpOptions: OtpRequest
): Promise<CatchErrorResult<OtpResponse>> {
  const [error, response] = await nativeFetch('/api/auth/otp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(otpOptions),
    credentials: 'include',
  });

  if (error) {
    return [error, undefined];
  }

  const [jsonErr, data] = await response
    .json()
    .catch((error) => [error as Error, undefined] as const);

  if (jsonErr) return [jsonErr, undefined];

  return [undefined, data];
}

// Verify OTP
export async function verifyOtp(
  otpOptions: OtpVerifyRequest
): Promise<CatchErrorResult<OtpVerifyResponse>> {
  const [error, response] = await nativeFetch('/api/auth/otp/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(otpOptions),
    credentials: 'include',
  }).catch((error) => [error as Error, undefined] as const);

  if (error) {
    return [error, undefined];
  }

  const [jsonErr, data] = await response
    .json()
    .catch((error) => [error as Error, undefined] as const);

  if (jsonErr) return [jsonErr, undefined];

  return [undefined, data];
}

// Upload media and get reference
export async function uploadMedia(
  fileUri: string
): Promise<{ imageRef: string }> {
  try {
    // Create FormData object
    const formData = new CustomFormData();

    // Extract filename from URI
    const filename = fileUri.split('/').pop() || 'image.jpg';
    // Get file extension
    const match = /\.(\w+)$/.exec(filename);
    const type = match ? `image/${match[1]}` : 'image/jpeg';

    console.log({
      uri: fileUri,
      name: filename,
      type,
    });

    // Add file to FormData
    // @ts-expect-error - FormData in React Native has slightly different implementation
    formData.append('file', {
      uri: fileUri,
      name: filename,
      type,
    });

    const response = await nativeFetch('/api/medias', {
      method: 'POST',
      body: formData,
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to upload media');
    }

    return response.json();
  } catch (error) {
    console.error('Error uploading media:', error);
    throw error;
  }
}
