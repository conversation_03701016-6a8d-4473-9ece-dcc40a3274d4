import {
  CatchErrorResult,
  native<PERSON><PERSON><PERSON>,
  HttpError,
  fetchJson,
  fetchExists,
} from '@/shared/lib';
import { CustomFormData } from '@/shared/model';

interface SignUpCredentials {
  email: string;
  password: string;
}

interface SignUpProfile {
  firstName: string;
  lastName: string;
  imageRef?: string;
}

interface SignUpData extends SignUpCredentials, SignUpProfile {}

interface OtpRequest {
  type:
    | 'email-verification'
    | 'sign-in'
    | 'forget-password'
    | 'update-password';
  userId: string;
}

interface OtpVerifyRequest {
  userId: string;
  otp: string;
  type: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isEmailVerified: boolean;
  imageRef?: string;
  createdAt: string;
  updatedAt: string;
}

export type SignUpResponse = {
  message: string;
  user: User;
};

export type OtpResponse = {
  message: string;
};

export type OtpVerifyResponse = {
  message: string;
  user: User;
};

// API Functions - return CatchErrorResult
export async function checkEmailExists(
  email: string
): Promise<CatchErrorResult<boolean, HttpError | Error>> {
  return fetchExists(`/api/users?email=${email}`);
}

export async function checkUserHasCredentials(
  email: string
): Promise<CatchErrorResult<boolean, HttpError | SyntaxError | Error>> {
  // Get the user
  const [userError, userData] = await fetchJson<{ user: { id: string } }>(
    `/api/users?email=${email}`
  );

  if (userError) {
    // If it's a 404, the user doesn't exist
    if (userError instanceof HttpError && userError.cause.status === 404) {
      return [undefined, false];
    }
    return [userError, undefined];
  }

  const userId = userData.user?.id;
  if (!userId) return [undefined, false];

  // Check credentials
  return fetchExists(`/api/users/${userId}/credentials`);
}

export async function signUp(
  data: SignUpData
): Promise<CatchErrorResult<SignUpResponse, HttpError | SyntaxError | Error>> {
  return fetchJson<SignUpResponse>('/api/auth/sign-up/email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
}

export async function sendOtp(
  otpOptions: OtpRequest
): Promise<CatchErrorResult<OtpResponse, HttpError | SyntaxError | Error>> {
  return fetchJson<OtpResponse>('/api/auth/otp', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(otpOptions),
  });
}

export async function verifyOtp(
  otpOptions: OtpVerifyRequest
): Promise<
  CatchErrorResult<OtpVerifyResponse, HttpError | SyntaxError | Error>
> {
  return fetchJson<OtpVerifyResponse>('/api/auth/otp/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(otpOptions),
  });
}

// Upload media and get reference
export async function uploadMedia(
  fileUri: string
): Promise<{ imageRef: string }> {
  try {
    // Create FormData object
    const formData = new CustomFormData();

    // Extract filename from URI
    const filename = fileUri.split('/').pop() || 'image.jpg';
    // Get file extension
    const match = /\.(\w+)$/.exec(filename);
    const type = match ? `image/${match[1]}` : 'image/jpeg';

    console.log({
      uri: fileUri,
      name: filename,
      type,
    });

    // Add file to FormData
    // @ts-expect-error - FormData in React Native has slightly different implementation
    formData.append('file', {
      uri: fileUri,
      name: filename,
      type,
    });

    const [error, response] = await nativeFetch('/api/medias', {
      method: 'POST',
      body: formData,
      credentials: 'include',
    });

    if (error) {
      throw new Error('Failed to upload media');
    }

    const [jsonError, data] = await response.json();
    if (jsonError) {
      throw jsonError;
    }

    return data;
  } catch (error) {
    console.error('Error uploading media:', error);
    throw error;
  }
}
